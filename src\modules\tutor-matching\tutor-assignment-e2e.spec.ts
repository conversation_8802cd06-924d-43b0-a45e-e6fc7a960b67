import { Test, TestingModule } from '@nestjs/testing';
import { TutorMatchingService } from './tutor-matching.service';
import { PlansService } from '../plans/plans.service';
import { DiaryEntryService } from '../diary/diary-entry.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { Plan } from '../../database/entities/plan.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Logger } from '@nestjs/common';

describe('Tutor Assignment - End-to-End Scenarios', () => {
  let tutorMatchingService: TutorMatchingService;
  let plansService: PlansService;
  let diaryEntryService: DiaryEntryService;
  let studentTutorMappingRepository: Repository<StudentTutorMapping>;
  let userRepository: Repository<User>;

  // Mock data
  const mockStudent = {
    id: 'student-e2e-1',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    type: UserType.STUDENT,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor1 = {
    id: 'tutor-e2e-1',
    name: 'Dr. Smith',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor2 = {
    id: 'tutor-e2e-2',
    name: 'Prof. Johnson',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockWritingFeature = {
    id: 'writing-feature',
    name: 'English Writing',
    type: 'MODULE',
    description: 'Writing module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockSpeakingFeature = {
    id: 'speaking-feature',
    name: 'English Speaking',
    type: 'MODULE',
    description: 'Speaking module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockDiaryFeature = {
    id: 'diary-feature',
    name: 'Diary Module',
    type: 'MODULE',
    description: 'Diary module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TutorMatchingService,
        PlansService,
        DiaryEntryService,
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Plan),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        // Mock other dependencies
        {
          provide: 'ChatService',
          useValue: {
            getOrCreateConversation: jest.fn(),
          },
        },
        {
          provide: 'NotificationHelper',
          useValue: {
            notify: jest.fn(),
          },
        },
        {
          provide: 'DeeplinkService',
          useValue: {
            getWebLink: jest.fn().mockReturnValue('http://test.com/diary/123'),
          },
        },
        {
          provide: 'UserSubscriptionRepository',
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: 'PaymentService',
          useValue: {
            processPayment: jest.fn(),
          },
        },
      ],
    }).compile();

    tutorMatchingService = module.get<TutorMatchingService>(TutorMatchingService);
    plansService = module.get<PlansService>(PlansService);
    diaryEntryService = module.get<DiaryEntryService>(DiaryEntryService);
    studentTutorMappingRepository = module.get<Repository<StudentTutorMapping>>(
      getRepositoryToken(StudentTutorMapping),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));

    // Mock logger
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  describe('Complete Plan Subscription Flow', () => {
    it('should assign same tutor to all features when new student subscribes to multi-feature plan', async () => {
      // Scenario: New student subscribes to a plan with Writing + Speaking features

      // Step 1: Mock no existing assignments (new student)
      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([]);

      // Step 2: Mock tutor selection (tutor1 has lowest workload)
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockTutor1, mockTutor2]),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Mock tutor1 has lower workload
      jest.spyOn(studentTutorMappingRepository, 'count')
        .mockResolvedValueOnce(1) // tutor1 workload
        .mockResolvedValueOnce(3); // tutor2 workload

      // Step 3: Mock feature validation
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);

      // Step 4: Mock no existing assignments for each feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Step 5: Mock assignment creation for both features
      const writingAssignment = {
        id: 'writing-assignment',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id,
        planFeatureId: mockWritingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping;

      const speakingAssignment = {
        id: 'speaking-assignment',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id, // Same tutor
        planFeatureId: mockSpeakingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'create')
        .mockReturnValueOnce(writingAssignment)
        .mockReturnValueOnce(speakingAssignment);

      jest.spyOn(studentTutorMappingRepository, 'save')
        .mockResolvedValueOnce(writingAssignment)
        .mockResolvedValueOnce(speakingAssignment);

      // Step 6: Execute plan subscription
      const writingResult = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockWritingFeature.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Mock that student now has writing assignment when assigning speaking
      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([writingAssignment]);

      const speakingResult = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockSpeakingFeature.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Step 7: Verify consistent tutor assignment
      expect(writingResult).toHaveLength(1);
      expect(speakingResult).toHaveLength(1);
      expect(writingResult[0].tutorId).toBe(mockTutor1.id);
      expect(speakingResult[0].tutorId).toBe(mockTutor1.id);
      expect(writingResult[0].tutorId).toBe(speakingResult[0].tutorId);
    });

    it('should maintain tutor consistency when existing student adds new features', async () => {
      // Scenario: Student with Writing tutor adds Speaking feature

      // Step 1: Mock existing writing assignment
      const existingWritingAssignment = {
        id: 'existing-writing',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id,
        planFeatureId: mockWritingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date('2024-01-01'),
        tutor: mockTutor1,
        planFeature: mockWritingFeature
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([existingWritingAssignment]);

      // Step 2: Mock no existing assignment for speaking feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Step 3: Mock assignment creation for speaking with same tutor
      const speakingAssignment = {
        id: 'new-speaking',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id, // Same tutor as existing assignment
        planFeatureId: mockSpeakingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(speakingAssignment);
      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(speakingAssignment);

      // Step 4: Execute new feature assignment
      const result = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockSpeakingFeature.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Step 5: Verify same tutor is used
      expect(result).toHaveLength(1);
      expect(result[0].tutorId).toBe(mockTutor1.id); // Same as existing assignment
    });
  });

  describe('Diary Entry Integration', () => {
    it('should use preferred tutor for diary auto-assignment', async () => {
      // Scenario: Student submits diary entry, needs tutor assignment for diary module

      // Step 1: Mock existing assignment in another feature
      const existingAssignment = {
        id: 'existing-writing',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id,
        planFeatureId: mockWritingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date('2024-01-01'),
        tutor: mockTutor1,
        planFeature: mockWritingFeature
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([existingAssignment]);

      // Step 2: Mock no existing assignment for diary feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Step 3: Mock diary assignment creation with same tutor
      const diaryAssignment = {
        id: 'diary-assignment',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id, // Same tutor as existing assignment
        planFeatureId: mockDiaryFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned during diary submission using preferred tutor logic'
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(diaryAssignment);

      // Step 4: Test preferred tutor selection
      const preferredTutor = await tutorMatchingService.getOrSelectPreferredTutor(mockStudent.id);

      // Step 5: Verify same tutor is selected
      expect(preferredTutor.id).toBe(mockTutor1.id);
    });

    it('should fallback to available tutor when preferred tutor selection fails', async () => {
      // Scenario: Preferred tutor selection fails during diary submission

      // Step 1: Mock preferred tutor selection failure
      jest.spyOn(studentTutorMappingRepository, 'find').mockRejectedValue(new Error('Database error'));

      // Step 2: Mock available tutors for fallback
      jest.spyOn(tutorMatchingService, 'getAvailableTutorsForModule').mockResolvedValue([mockTutor2]);

      // Step 3: Mock diary assignment creation with fallback tutor
      const diaryAssignment = {
        id: 'diary-assignment-fallback',
        studentId: mockStudent.id,
        tutorId: mockTutor2.id, // Fallback tutor
        planFeatureId: mockDiaryFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned during diary submission (fallback from preferred tutor failure)'
      } as StudentTutorMapping;

      jest.spyOn(tutorMatchingService, 'assignTutor').mockResolvedValue(diaryAssignment);
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(diaryAssignment);

      // Step 4: Test fallback behavior
      try {
        await tutorMatchingService.getOrSelectPreferredTutor(mockStudent.id);
      } catch (error) {
        // Expected to fail, triggering fallback in diary service
        expect(error.message).toBe('Database error');
      }

      // Step 5: Verify fallback tutor assignment would work
      expect(tutorMatchingService.getAvailableTutorsForModule).toHaveBeenCalledWith(mockDiaryFeature.id);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle preferred tutor selection failure gracefully', async () => {
      // Scenario: Preferred tutor selection fails, system falls back to regular selection

      // Step 1: Mock database error when getting student assignments
      jest.spyOn(studentTutorMappingRepository, 'find').mockRejectedValue(new Error('Database connection failed'));

      // Step 2: Mock fallback tutor selection
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockTutor2]),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
      jest.spyOn(studentTutorMappingRepository, 'count').mockResolvedValue(0);

      // Step 3: Mock assignment creation with fallback tutor
      const fallbackAssignment = {
        id: 'fallback-assignment',
        studentId: mockStudent.id,
        tutorId: mockTutor2.id,
        planFeatureId: mockWritingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(fallbackAssignment);
      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(fallbackAssignment);

      // Step 4: Execute assignment with error handling
      const result = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockWritingFeature.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Step 5: Verify fallback assignment succeeded
      expect(result).toHaveLength(1);
      expect(result[0].tutorId).toBe(mockTutor2.id); // Fallback tutor used
    });

    it('should continue with other students when individual assignment fails', async () => {
      // Scenario: Batch assignment where one student fails, others succeed

      const mockStudent2 = {
        id: 'student-e2e-2',
        name: 'Bob Wilson',
        email: '<EMAIL>',
        type: UserType.STUDENT
      } as User;

      // Step 1: Mock successful assignment for first student
      jest.spyOn(studentTutorMappingRepository, 'find')
        .mockResolvedValueOnce([]) // student1 - no existing assignments
        .mockResolvedValueOnce([]); // student2 - no existing assignments

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockTutor1]),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
      jest.spyOn(studentTutorMappingRepository, 'count').mockResolvedValue(0);

      // Step 2: Mock assignment creation - success for student1, failure for student2
      const successfulAssignment = {
        id: 'successful-assignment',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id,
        planFeatureId: mockWritingFeature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping;

      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(studentTutorMappingRepository, 'create')
        .mockReturnValueOnce(successfulAssignment)
        .mockImplementationOnce(() => {
          throw new Error('Assignment creation failed');
        });
      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(successfulAssignment);

      // Step 3: Execute batch assignment
      const result = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockWritingFeature.id,
        studentIds: [mockStudent.id, mockStudent2.id],
        reassignExisting: false
      });

      // Step 4: Verify partial success (only successful assignments returned)
      expect(result).toHaveLength(1);
      expect(result[0].studentId).toBe(mockStudent.id);
      expect(result[0].tutorId).toBe(mockTutor1.id);
    });
  });
});
