import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import {
  AssignTutorDto,
  UpdateTutorAssignmentDto,
  TutorAssignmentResponseDto,
  StudentTutorDto,
  TutorStudentDto,
  TutorWorkloadDto,
  AssignmentFilterDto,
  AutoAssignTutorsDto,
  StudentTutorFilterDto
} from '../../database/models/tutor-matching.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { ChatService } from '../chat/chat.service';

@Injectable()
export class TutorMatchingService {
  private readonly logger = new Logger(TutorMatchingService.name);

  constructor(
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    private readonly notificationHelper: NotificationHelperService,
    private readonly chatService: ChatService
  ) {}

  /**
   * Assign a tutor to a student for a specific module
   * @param assignTutorDto Assignment data
   * @returns Created assignment
   */
  async assignTutor(assignTutorDto: AssignTutorDto): Promise<TutorAssignmentResponseDto> {
    try {
      // Validate student
      const student = await this.userRepository.findOne({ where: { id: assignTutorDto.studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${assignTutorDto.studentId} not found`);
      }
      if (student.type !== UserType.STUDENT) {
        throw new BadRequestException(`User with ID ${assignTutorDto.studentId} is not a student`);
      }

      // Validate tutor
      const tutor = await this.userRepository.findOne({
        where: { id: assignTutorDto.tutorId },
        relations: ['userRoles', 'userRoles.role']
      });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${assignTutorDto.tutorId} not found`);
      }
      const isTutor = tutor.userRoles.some(userRole => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new BadRequestException(`User with ID ${assignTutorDto.tutorId} is not a tutor`);
      }

      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: assignTutorDto.planFeatureId}
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${assignTutorDto.planFeatureId} not found`);
      }

      // Check if student already has a tutor for this module
      const existingMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: assignTutorDto.studentId,
          planFeatureId: assignTutorDto.planFeatureId,
          status: MappingStatus.ACTIVE
        }
      });

      if (existingMapping) {
        throw new ConflictException(`Student already has an active tutor for this module`);
      }

      // Create mapping
      const mapping = this.studentTutorMappingRepository.create({
        studentId: assignTutorDto.studentId,
        tutorId: assignTutorDto.tutorId,
        planFeatureId: assignTutorDto.planFeatureId,
        status: MappingStatus.ACTIVE,
        assignedDate: getCurrentUTCDate(),
        notes: assignTutorDto.notes
      });

      const savedMapping = await this.studentTutorMappingRepository.save(mapping);

      // Create a chat conversation between student and tutor
      try {
        const conversation = await this.chatService.getOrCreateConversation(
          student.id,
          tutor.id
        );
        this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
      } catch (error) {
        // Log the error but don't fail the whole operation
        this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
      }

      // Send notification to student
      await this.notificationHelper.notify(
        student.id,
        NotificationType.TUTOR_ASSIGNMENT,
        'New Tutor Assigned',
        `You have been assigned ${tutor.name} as your tutor for ${moduleFeature.name}.`,
        {
          relatedEntityId: savedMapping.id,
          relatedEntityType: 'student_tutor_mapping',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );

      // Send notification to tutor
      await this.notificationHelper.notify(
        tutor.id,
        NotificationType.TUTOR_ASSIGNMENT,
        'New Student Assigned',
        `You have been assigned as a tutor for ${student.name} in ${moduleFeature.name}.`,
        {
          relatedEntityId: savedMapping.id,
          relatedEntityType: 'student_tutor_mapping',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false
        }
      );

      return this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature);
    } catch (error) {
      this.logger.error(`Error assigning tutor: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a tutor assignment
   * @param id Assignment ID
   * @param updateDto Update data
   * @returns Updated assignment
   */
  async updateAssignment(id: string, updateDto: UpdateTutorAssignmentDto): Promise<TutorAssignmentResponseDto> {
    try {
      // Find assignment
      const assignment = await this.studentTutorMappingRepository.findOne({
        where: { id },
        relations: ['student', 'tutor', 'planFeature']
      });

      if (!assignment) {
        throw new NotFoundException(`Assignment with ID ${id} not found`);
      }

      // If changing tutor, validate new tutor
      if (updateDto.tutorId && updateDto.tutorId !== assignment.tutorId) {
        const newTutor = await this.userRepository.findOne({
          where: { id: updateDto.tutorId },
          relations: ['userRoles', 'userRoles.role']
        });

        if (!newTutor) {
          throw new NotFoundException(`Tutor with ID ${updateDto.tutorId} not found`);
        }

        const isTutor = newTutor.userRoles.some(userRole => userRole.role.name === 'tutor');
        if (!isTutor) {
          throw new BadRequestException(`User with ID ${updateDto.tutorId} is not a tutor`);
        }

        assignment.tutorId = updateDto.tutorId;
        assignment.tutor = newTutor;

        // Send notification to student about tutor change
        await this.notificationHelper.notify(
          assignment.studentId,
          NotificationType.TUTOR_ASSIGNMENT,
          'Tutor Changed',
          `Your tutor for ${assignment.planFeature.name} has been changed to ${newTutor.name}.`,
          {
            relatedEntityId: assignment.id,
            relatedEntityType: 'student_tutor_mapping',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );

        // Send notification to new tutor
        await this.notificationHelper.notify(
          newTutor.id,
          NotificationType.TUTOR_ASSIGNMENT,
          'New Student Assigned',
          `You have been assigned as a tutor for ${assignment.student.name} in ${assignment.planFeature.name}.`,
          {
            relatedEntityId: assignment.id,
            relatedEntityType: 'student_tutor_mapping',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      }

      // Update status if provided
      if (updateDto.status) {
        assignment.status = updateDto.status;
      }

      // Update notes if provided
      if (updateDto.notes !== undefined) {
        assignment.notes = updateDto.notes;
      }

      const updatedAssignment = await this.studentTutorMappingRepository.save(assignment);

      return this.mapToAssignmentResponseDto(
        updatedAssignment,
        assignment.student,
        assignment.tutor,
        assignment.planFeature
      );
    } catch (error) {
      this.logger.error(`Error updating assignment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all tutor assignments with filtering and pagination
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of assignments
   */
  async getAllAssignments(
    filterDto?: AssignmentFilterDto,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<TutorAssignmentResponseDto>> {
    try {
      // Build query
      let query = this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoinAndSelect('mapping.student', 'student')
        .leftJoinAndSelect('mapping.tutor', 'tutor')
        .leftJoinAndSelect('mapping.planFeature', 'planFeature');

      // Apply filters
      if (filterDto) {
        if (filterDto.studentName) {
          query = query.andWhere('student.name ILIKE :studentName', {
            studentName: `%${filterDto.studentName}%`
          });
        }

        if (filterDto.tutorName) {
          query = query.andWhere('tutor.name ILIKE :tutorName', {
            tutorName: `%${filterDto.tutorName}%`
          });
        }

        if (filterDto.planFeatureId) {
          query = query.andWhere('mapping.planFeatureId = :planFeatureId', {
            planFeatureId: filterDto.planFeatureId
          });
        }

        if (filterDto.status) {
          query = query.andWhere('mapping.status = :status', {
            status: filterDto.status
          });
        }
      }

      // Get total count
      const totalCount = await query.getCount();

      // Apply pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        query = query
          .skip(skip)
          .take(limit);

        if (sortBy && sortDirection) {
          query = query.orderBy(`mapping.${sortBy}`, sortDirection);
        } else {
          query = query.orderBy('mapping.createdAt', 'DESC');
        }
      } else {
        query = query.orderBy('mapping.createdAt', 'DESC');
      }

      // Execute query
      const assignments = await query.getMany();

      // Map to response DTOs
      const assignmentDtos = assignments.map(assignment =>
        this.mapToAssignmentResponseDto(
          assignment,
          assignment.student,
          assignment.tutor,
          assignment.planFeature
        )
      );

      return new PagedListDto(assignmentDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting assignments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific assignment by ID
   * @param id Assignment ID
   * @returns Assignment details
   */
  async getAssignmentById(id: string): Promise<TutorAssignmentResponseDto> {
    try {
      const assignment = await this.studentTutorMappingRepository.findOne({
        where: { id },
        relations: ['student', 'tutor', 'planFeature']
      });

      if (!assignment) {
        throw new NotFoundException(`Assignment with ID ${id} not found`);
      }

      return this.mapToAssignmentResponseDto(
        assignment,
        assignment.student,
        assignment.tutor,
        assignment.planFeature
      );
    } catch (error) {
      this.logger.error(`Error getting assignment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the tutor assigned to a student for a specific module
   * @param studentId Student ID
   * @param moduleId Module ID (PlanFeature ID)
   * @returns Tutor assignment or null if none exists
   */
  async getStudentTutorForModule(studentId: string, moduleId: string): Promise<StudentTutorMapping | null> {
    try {
      if (!moduleId) {
        this.logger.warn(`No module ID provided for student ${studentId}`);
        return null;
      }

      return await this.studentTutorMappingRepository.findOne({
        where: {
          studentId,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor', 'planFeature']
      });
    } catch (error) {
      this.logger.error(`Error getting tutor for student ${studentId} and module ${moduleId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get available tutors for a specific module
   * @param moduleId Module ID (PlanFeature ID)
   * @returns List of available tutors
   */
  async getAvailableTutorsForModule(moduleId: string): Promise<User[]> {
    try {
      if (!moduleId) {
        this.logger.warn('No module ID provided for getting available tutors');
        return [];
      }

      this.logger.log(`Finding available tutors for module ${moduleId}`);

      // Get all users with tutor role
      const tutors = await this.userRepository
        .createQueryBuilder('user')
        .innerJoin('user.userRoles', 'userRole')
        .innerJoin('userRole.role', 'role')
        .where('role.name = :roleName', { roleName: 'tutor' })
        .andWhere('user.isActive = :isActive', { isActive: true })
        .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
        .getMany();

      this.logger.log(`Found ${tutors.length} active tutors`);

      // Filter tutors by workload (in a real implementation, you might want to check their current assignments)
      // For now, just return all active tutors
      return tutors;
    } catch (error) {
      this.logger.error(`Error getting available tutors for module ${moduleId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get all tutors assigned to a student with filtering and pagination
   * @param studentId Student ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of tutors
   */
  async getStudentTutors(
    studentId: string,
    filterDto?: StudentTutorFilterDto,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<StudentTutorDto>> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({ where: { id: studentId } });
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Build query
      let query = this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoinAndSelect('mapping.tutor', 'tutor')
        .leftJoinAndSelect('mapping.planFeature', 'planFeature')
        .where('mapping.studentId = :studentId', { studentId });

      // Apply filters
      if (filterDto) {
        if (filterDto.tutorName) {
          query = query.andWhere('tutor.name ILIKE :tutorName', {
            tutorName: `%${filterDto.tutorName}%`
          });
        }

        if (filterDto.planFeatureId) {
          query = query.andWhere('mapping.planFeatureId = :planFeatureId', {
            planFeatureId: filterDto.planFeatureId
          });
        }

        if (filterDto.status) {
          query = query.andWhere('mapping.status = :status', {
            status: filterDto.status
          });
        }

        if(filterDto.planFeatureType) {
          query = query.andWhere('planFeature.type = :planFeatureType', {
            planFeatureType: filterDto.planFeatureType
          });
        }
      }

      // Get total count
      const totalCount = await query.getCount();

      // Apply pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        query = query
          .skip(skip)
          .take(limit);

        if (sortBy && sortDirection) {
          if (sortBy === 'name') {
            query = query.orderBy('tutor.name', sortDirection);
          } else if (sortBy === 'moduleName') {
            query = query.orderBy('planFeature.name', sortDirection);
          } else {
            query = query.orderBy(`mapping.${sortBy}`, sortDirection);
          }
        } else {
          query = query.orderBy('mapping.assignedDate', 'DESC');
        }
      } else {
        query = query.orderBy('mapping.assignedDate', 'DESC');
      }

      // Execute query
      const assignments = await query.getMany();

      // Map to DTOs
      const tutorDtos = assignments.map(assignment => ({
        id: assignment.tutorId,
        name: assignment.tutor.name,
        email: assignment.tutor.email,
        profilePicture: assignment.tutor.profilePicture,
        bio: assignment.tutor.bio,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate
      }));

      return new PagedListDto<StudentTutorDto>(tutorDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting student tutors: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all students assigned to a tutor
   * @param tutorId Tutor ID
   * @returns List of students
   */
  async getTutorStudents(tutorId: string): Promise<TutorStudentDto[]> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      // Get assignments
      const assignments = await this.studentTutorMappingRepository.find({
        where: { tutorId },
        relations: ['student', 'planFeature']
      });

      // Map to DTOs
      return assignments.map(assignment => ({
        id: assignment.studentId,
        name: assignment.student.name,
        email: assignment.student.email,
        profilePicture: assignment.student.profilePicture,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
        lastActivityDate: assignment.lastActivityDate
      }));
    } catch (error) {
      this.logger.error(`Error getting tutor students: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get tutor workload statistics
   * @returns List of tutors with workload stats
   */
  async getTutorWorkloads(): Promise<TutorWorkloadDto[]> {
    try {
      // Get all tutors
      const tutors = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.userRoles', 'userRoles')
        .leftJoinAndSelect('userRoles.role', 'role')
        .where('role.name = :roleName', { roleName: 'tutor' })
        .getMany();

      // Get workload for each tutor
      const workloads = await Promise.all(tutors.map(async tutor => {
        // Count active students
        const activeStudentCount = await this.studentTutorMappingRepository.count({
          where: {
            tutorId: tutor.id,
            status: MappingStatus.ACTIVE
          }
        });

        // Count pending reviews
        const pendingReviewCount = await this.diaryEntryRepository.count({
          where: {
            status: DiaryEntryStatus.SUBMIT,
            diary: {
              user: {
                id: In(
                  await this.studentTutorMappingRepository
                    .createQueryBuilder('mapping')
                    .select('mapping.studentId')
                    .where('mapping.tutorId = :tutorId', { tutorId: tutor.id })
                    .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
                    .getMany()
                    .then(mappings => mappings.map(m => m.studentId))
                )
              }
            }
          }
        });

        // Get last activity date
        const lastMapping = await this.studentTutorMappingRepository.findOne({
          where: { tutorId: tutor.id },
          order: { lastActivityDate: 'DESC' }
        });

        return {
          id: tutor.id,
          name: tutor.name,
          activeStudentCount,
          pendingReviewCount,
          lastActivityDate: lastMapping?.lastActivityDate
        };
      }));

      return workloads;
    } catch (error) {
      this.logger.error(`Error getting tutor workloads: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Auto-assign tutors to students based on workload balancing
   * @param autoAssignDto Auto-assignment parameters
   * @returns List of created assignments
   */
  async autoAssignTutors(autoAssignDto: AutoAssignTutorsDto): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId, type: FeatureType.MODULE }
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE
          }
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // First try to get the student's preferred tutor
          let tutor: User;
          try {
            tutor = await this.getOrSelectPreferredTutor(student.id);
            this.logger.log(`Using preferred tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          } catch (preferredTutorError) {
            // Fallback to module-specific selection if preferred tutor selection fails
            this.logger.warn(`Failed to get preferred tutor for student ${student.id}, falling back to module-specific selection: ${preferredTutorError.message}`);
            tutor = await this.selectTutorForModule(autoAssignDto.planFeatureId);
            this.logger.log(`Using fallback tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preferred tutor logic'
          });

          const savedMapping = await this.studentTutorMappingRepository.save(mapping);
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

        // Create a chat conversation between student and tutor
        try {
          const conversation = await this.chatService.getOrCreateConversation(
            student.id,
            tutor.id
          );
          this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
        } catch (error) {
          // Log the error but don't fail the whole operation
          this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
        }

        // Send notifications
        await this.notificationHelper.notify(
          student.id,
          NotificationType.TUTOR_ASSIGNMENT,
          'New Tutor Assigned',
          `You have been assigned ${tutor.name} as your tutor for ${moduleFeature.name}.`,
          {
            relatedEntityId: savedMapping.id,
            relatedEntityType: 'student_tutor_mapping',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );

        await this.notificationHelper.notify(
          tutor.id,
          NotificationType.TUTOR_ASSIGNMENT,
          'New Student Assigned',
          `You have been assigned as a tutor for ${student.name} in ${moduleFeature.name}.`,
          {
            relatedEntityId: savedMapping.id,
            relatedEntityType: 'student_tutor_mapping',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );

          // Add to result
          assignments.push(
            this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature)
          );
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Select the most appropriate tutor for a module with preference for different tutors
   * @param moduleId Module ID to assign tutor for
   * @param excludeTutorIds Tutor IDs to avoid (prefer different tutors)
   * @returns Selected tutor with the lowest workload, preferring non-excluded tutors
   */
  async selectTutorForModuleWithPreference(moduleId: string, excludeTutorIds: string[] = []): Promise<User> {
    // Get all active and confirmed tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .getMany();

    this.logger.log(`Found ${tutors.length} active and confirmed tutors for module ${moduleId} with preference logic`);

    if (tutors.length === 0) {
      this.logger.error(`No active and confirmed tutors available for assignment to module ${moduleId}`);
      throw new BadRequestException('No active and confirmed tutors available for assignment');
    }

    // Get tutor workloads
    const tutorWorkloads = await Promise.all(tutors.map(async tutor => {
      // Count active students for this tutor
      const activeStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          status: MappingStatus.ACTIVE
        }
      });

      // Count active students for this tutor in this specific module
      const moduleStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE
        }
      });

      const isExcluded = excludeTutorIds.includes(tutor.id);

      return {
        tutor,
        activeStudentCount,
        moduleStudentCount,
        isExcluded
      };
    }));

    // Sort tutors with preference logic:
    // 1. Prefer non-excluded tutors
    // 2. Then by module-specific workload
    // 3. Then by overall workload
    tutorWorkloads.sort((a, b) => {
      // First, prefer non-excluded tutors
      if (a.isExcluded !== b.isExcluded) {
        return a.isExcluded ? 1 : -1; // Non-excluded tutors come first
      }

      // If both are excluded or both are not excluded, compare workloads
      // First compare module-specific workload
      if (a.moduleStudentCount !== b.moduleStudentCount) {
        return a.moduleStudentCount - b.moduleStudentCount;
      }
      // If module workloads are equal, compare overall workload
      return a.activeStudentCount - b.activeStudentCount;
    });

    const selectedTutor = tutorWorkloads[0];

    if (selectedTutor.isExcluded && excludeTutorIds.length > 0) {
      this.logger.warn(`All preferred tutors are unavailable, assigning excluded tutor ${selectedTutor.tutor.name} for module ${moduleId}`);
    } else if (!selectedTutor.isExcluded && excludeTutorIds.length > 0) {
      this.logger.log(`Successfully assigned different tutor ${selectedTutor.tutor.name} for module ${moduleId}, avoiding [${excludeTutorIds.join(', ')}]`);
    }

    // Return the tutor with the best preference and lowest workload
    return selectedTutor.tutor;
  }

  /**
   * Get all active tutor assignments for a student
   * @param studentId Student ID
   * @returns List of active tutor assignments
   */
  async getStudentTutorAssignments(studentId: string): Promise<StudentTutorMapping[]> {
    try {
      return await this.studentTutorMappingRepository.find({
        where: {
          studentId,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor', 'planFeature'],
        order: { assignedDate: 'ASC' } // Oldest assignment first (preferred tutor)
      });
    } catch (error) {
      this.logger.error(`Error getting student tutor assignments for ${studentId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get or select a preferred tutor for a student
   * If student has existing tutors, returns the first assigned tutor (preferred)
   * If no existing tutors, selects a new tutor based on workload
   * @param studentId Student ID
   * @returns Preferred tutor for the student
   */
  async getOrSelectPreferredTutor(studentId: string): Promise<User> {
    try {
      // First, check if student already has any tutor assignments
      const existingAssignments = await this.getStudentTutorAssignments(studentId);

      if (existingAssignments.length > 0) {
        // Return the first assigned tutor (oldest assignment = preferred tutor)
        const preferredTutor = existingAssignments[0].tutor;
        this.logger.log(`Found existing preferred tutor ${preferredTutor.name} (${preferredTutor.id}) for student ${studentId}`);
        return preferredTutor;
      }

      // No existing assignments, select a new tutor based on overall workload
      this.logger.log(`No existing tutors for student ${studentId}, selecting new preferred tutor`);
      return await this.selectTutorByOverallWorkload();
    } catch (error) {
      this.logger.error(`Error getting or selecting preferred tutor for student ${studentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Select a tutor based on overall workload (not module-specific)
   * Used for selecting a preferred tutor for new students
   * @returns Selected tutor with the lowest overall workload
   */
  async selectTutorByOverallWorkload(): Promise<User> {
    // Get all active and confirmed tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .getMany();

    if (tutors.length === 0) {
      throw new NotFoundException('No active tutors available');
    }

    // Calculate overall workload for each tutor
    const tutorWorkloads = [];
    for (const tutor of tutors) {
      const activeStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          status: MappingStatus.ACTIVE
        }
      });

      tutorWorkloads.push({
        tutor,
        activeStudentCount
      });
    }

    // Sort tutors by overall workload
    tutorWorkloads.sort((a, b) => a.activeStudentCount - b.activeStudentCount);

    // Return the tutor with the lowest overall workload
    return tutorWorkloads[0].tutor;
  }

  /**
   * Select the most appropriate tutor for a module based on workload balancing
   * @param moduleId Module ID to assign tutor for
   * @returns Selected tutor with the lowest workload
   */
  async selectTutorForModule(moduleId: string): Promise<User> {
    // Get all active and confirmed tutors
    const tutors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .where('role.name = :roleName', { roleName: 'tutor' })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isConfirmed = :isConfirmed', { isConfirmed: true })
      .getMany();

    this.logger.log(`Found ${tutors.length} active and confirmed tutors for module ${moduleId}`);

    if (tutors.length === 0) {
      this.logger.error(`No active and confirmed tutors available for assignment to module ${moduleId}`);
      throw new BadRequestException('No active and confirmed tutors available for assignment');
    }

    // Get tutor workloads
    const tutorWorkloads = await Promise.all(tutors.map(async tutor => {
      // Count active students for this tutor
      const activeStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          status: MappingStatus.ACTIVE
        }
      });

      // Count active students for this tutor in this specific module
      const moduleStudentCount = await this.studentTutorMappingRepository.count({
        where: {
          tutorId: tutor.id,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE
        }
      });

      return {
        tutor,
        activeStudentCount,
        moduleStudentCount
      };
    }));

    // Sort tutors by module-specific workload first, then by overall workload
    tutorWorkloads.sort((a, b) => {
      // First compare module-specific workload
      if (a.moduleStudentCount !== b.moduleStudentCount) {
        return a.moduleStudentCount - b.moduleStudentCount;
      }
      // If module workloads are equal, compare overall workload
      return a.activeStudentCount - b.activeStudentCount;
    });

    // Return the tutor with the lowest workload
    return tutorWorkloads[0].tutor;
  }

  /**
   * Auto-assign tutors to students with preference for different tutors (for plan upgrades)
   * @param autoAssignDto Auto-assignment parameters with excludeTutorIds
   * @returns List of created assignments
   */
  async autoAssignTutorsWithPreference(autoAssignDto: AutoAssignTutorsDto & { excludeTutorIds?: string[] }): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId }
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      this.logger.log(`Starting tutor assignment with preference for module ${moduleFeature.name} (${autoAssignDto.planFeatureId}), excluding tutors: [${(autoAssignDto.excludeTutorIds || []).join(', ')}]`);

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE
          }
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // First try to get the student's preferred tutor, but respect excludeTutorIds
          let tutor: User;
          try {
            const preferredTutor = await this.getOrSelectPreferredTutor(student.id);
            const excludeTutorIds = autoAssignDto.excludeTutorIds || [];

            // Check if preferred tutor is in the exclude list
            if (excludeTutorIds.includes(preferredTutor.id)) {
              this.logger.log(`Preferred tutor ${preferredTutor.name} (${preferredTutor.id}) is excluded for student ${student.id}, selecting alternative`);
              tutor = await this.selectTutorForModuleWithPreference(
                autoAssignDto.planFeatureId,
                excludeTutorIds
              );
            } else {
              tutor = preferredTutor;
              this.logger.log(`Using preferred tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
            }
          } catch (preferredTutorError) {
            // Fallback to preference-based selection if preferred tutor selection fails
            this.logger.warn(`Failed to get preferred tutor for student ${student.id}, falling back to preference-based selection: ${preferredTutorError.message}`);
            tutor = await this.selectTutorForModuleWithPreference(
              autoAssignDto.planFeatureId,
              autoAssignDto.excludeTutorIds || []
            );
          }

          this.logger.log(`Selected tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);

          // Double-check for existing assignment to prevent race conditions
          const existingAssignment = await this.studentTutorMappingRepository.findOne({
            where: {
              studentId: student.id,
              planFeatureId: autoAssignDto.planFeatureId,
              status: MappingStatus.ACTIVE
            }
          });

          if (existingAssignment) {
            this.logger.warn(`Race condition detected: Student ${student.id} already has assignment for module ${autoAssignDto.planFeatureId}, skipping`);
            continue;
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preference logic'
          });

          const savedMapping = await this.studentTutorMappingRepository.save(mapping);
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

        // Create a chat conversation between student and tutor
        try {
          const conversation = await this.chatService.getOrCreateConversation(
            student.id,
            tutor.id
          );
          this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
        } catch (error) {
          // Log the error but don't fail the whole operation
          this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
        }

          // Add to result (no notifications sent here)
          assignments.push(
            this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature)
          );
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors with preference: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Auto-assign tutors to students based on workload balancing without sending notifications
   * This is used when we want to collect all assignments first and then send consolidated notifications
   * @param autoAssignDto Auto-assignment parameters
   * @returns List of created assignments
   */
  async autoAssignTutorsWithoutNotifications(autoAssignDto: AutoAssignTutorsDto): Promise<TutorAssignmentResponseDto[]> {
    try {
      // Validate module feature
      const moduleFeature = await this.planFeatureRepository.findOne({
        where: { id: autoAssignDto.planFeatureId }
      });
      if (!moduleFeature) {
        throw new NotFoundException(`Module feature with ID ${autoAssignDto.planFeatureId} not found`);
      }

      // Filter students who need assignment
      const studentsToAssign = [];
      for (const studentId of autoAssignDto.studentIds) {
        // Check if student exists
        const student = await this.userRepository.findOne({ where: { id: studentId } });
        if (!student) {
          this.logger.warn(`Student with ID ${studentId} not found, skipping`);
          continue;
        }
        if (student.type !== UserType.STUDENT) {
          this.logger.warn(`User with ID ${studentId} is not a student, skipping`);
          continue;
        }

        // Check if student already has a tutor for this module
        const existingMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE
          }
        });

        if (existingMapping && !autoAssignDto.reassignExisting) {
          this.logger.warn(`Student ${studentId} already has a tutor for this module, skipping`);
          continue;
        }

        studentsToAssign.push(student);
      }

      if (studentsToAssign.length === 0) {
        throw new BadRequestException('No students available for assignment');
      }

      // Assign tutors to each student
      const assignments = [];

      for (const student of studentsToAssign) {
        try {
          // First try to get the student's preferred tutor
          let tutor: User;
          try {
            tutor = await this.getOrSelectPreferredTutor(student.id);
            this.logger.log(`Using preferred tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          } catch (preferredTutorError) {
            // Fallback to module-specific selection if preferred tutor selection fails
            this.logger.warn(`Failed to get preferred tutor for student ${student.id}, falling back to module-specific selection: ${preferredTutorError.message}`);
            tutor = await this.selectTutorForModule(autoAssignDto.planFeatureId);
            this.logger.log(`Using fallback tutor ${tutor.name} (${tutor.id}) for student ${student.id} in module ${moduleFeature.name}`);
          }

          // Create assignment
          const mapping = this.studentTutorMappingRepository.create({
            studentId: student.id,
            tutorId: tutor.id,
            planFeatureId: autoAssignDto.planFeatureId,
            status: MappingStatus.ACTIVE,
            assignedDate: getCurrentUTCDate(),
            notes: 'Auto-assigned by system with preferred tutor logic'
          });

          const savedMapping = await this.studentTutorMappingRepository.save(mapping);
          this.logger.log(`Successfully created assignment ${savedMapping.id} for student ${student.id} with tutor ${tutor.name}`);

          // Create a chat conversation between student and tutor
          try {
            const conversation = await this.chatService.getOrCreateConversation(
              student.id,
              tutor.id
            );
            this.logger.log(`Created or retrieved chat conversation ${conversation.id} between student ${student.id} and tutor ${tutor.id}`);
          } catch (error) {
            // Log the error but don't fail the whole operation
            this.logger.error(`Error creating chat conversation: ${error.message}`, error.stack);
          }

          // Add to result
          assignments.push(
            this.mapToAssignmentResponseDto(savedMapping, student, tutor, moduleFeature)
          );
        } catch (studentError) {
          this.logger.error(`Failed to assign tutor for student ${student.id} in module ${moduleFeature.name}: ${studentError.message}`, studentError.stack);
          // Continue with other students - don't fail the entire batch
        }
      }

      return assignments;
    } catch (error) {
      this.logger.error(`Error auto-assigning tutors without notifications: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the last activity date for a student-tutor mapping
   * @param studentId Student ID
   * @param moduleId Module ID
   * @returns Updated mapping
   */
  async updateLastActivityDate(studentId: string, moduleId: string): Promise<void> {
    try {
      const mapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId,
          planFeatureId: moduleId,
          status: MappingStatus.ACTIVE
        }
      });

      if (mapping) {
        mapping.lastActivityDate = getCurrentUTCDate();
        await this.studentTutorMappingRepository.save(mapping);
      }
    } catch (error) {
      this.logger.error(`Error updating last activity date: ${error.message}`, error.stack);
      // Don't throw error, just log it
    }
  }

  /**
   * Map entities to assignment response DTO
   * @param mapping Assignment entity
   * @param student Student entity
   * @param tutor Tutor entity
   * @param planFeature Plan feature entity
   * @returns Assignment response DTO
   */
  private mapToAssignmentResponseDto(
    mapping: StudentTutorMapping,
    student: User,
    tutor: User,
    planFeature: PlanFeature
  ): TutorAssignmentResponseDto {
    return {
      id: mapping.id,
      studentId: mapping.studentId,
      studentName: student.name,
      tutorId: mapping.tutorId,
      tutorName: tutor.name,
      planFeatureId: mapping.planFeatureId,
      moduleName: planFeature.name,
      moduleType: planFeature.type,
      status: mapping.status,
      assignedDate: mapping.assignedDate,
      lastActivityDate: mapping.lastActivityDate,
      notes: mapping.notes,
      createdAt: mapping.createdAt,
      updatedAt: mapping.updatedAt
    };
  }
}
