import { Test, TestingModule } from '@nestjs/testing';
import { TutorMatchingService } from './tutor-matching.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Logger } from '@nestjs/common';

describe('Tutor Assignment - Performance Tests', () => {
  let service: TutorMatchingService;
  let studentTutorMappingRepository: Repository<StudentTutorMapping>;
  let userRepository: Repository<User>;

  // Generate mock data for performance testing
  const generateMockStudents = (count: number): User[] => {
    return Array.from({ length: count }, (_, i) => ({
      id: `student-perf-${i + 1}`,
      name: `Student ${i + 1}`,
      email: `student${i + 1}@example.com`,
      type: UserType.STUDENT,
      isActive: true,
      isConfirmed: true
    } as User));
  };

  const generateMockTutors = (count: number): User[] => {
    return Array.from({ length: count }, (_, i) => ({
      id: `tutor-perf-${i + 1}`,
      name: `Tutor ${i + 1}`,
      email: `tutor${i + 1}@example.com`,
      type: UserType.TUTOR,
      isActive: true,
      isConfirmed: true
    } as User));
  };

  const mockFeature = {
    id: 'perf-feature-1',
    name: 'Performance Test Feature',
    type: 'MODULE',
    description: 'Test feature',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TutorMatchingService,
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: 'ChatService',
          useValue: {
            getOrCreateConversation: jest.fn(),
          },
        },
        {
          provide: 'NotificationHelperService',
          useValue: {
            notify: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TutorMatchingService>(TutorMatchingService);
    studentTutorMappingRepository = module.get<Repository<StudentTutorMapping>>(
      getRepositoryToken(StudentTutorMapping),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));

    // Mock logger
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  describe('Batch Assignment Performance', () => {
    it('should handle large batch of new student assignments efficiently', async () => {
      // Test with 100 new students
      const students = generateMockStudents(100);
      const tutors = generateMockTutors(10);
      const studentIds = students.map(s => s.id);

      // Mock no existing assignments for all students
      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([]);

      // Mock tutor selection
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(tutors),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Mock balanced tutor workloads
      jest.spyOn(studentTutorMappingRepository, 'count').mockImplementation((options: any) => {
        const tutorId = options.where.tutorId;
        const tutorIndex = tutors.findIndex(t => t.id === tutorId);
        return Promise.resolve(tutorIndex * 2); // Simulate different workloads
      });

      // Mock feature and student validation
      jest.spyOn(userRepository, 'findOne').mockImplementation((options: any) => {
        const studentId = options.where.id;
        return Promise.resolve(students.find(s => s.id === studentId));
      });

      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Mock assignment creation
      jest.spyOn(studentTutorMappingRepository, 'create').mockImplementation((data: any) => ({
        id: `assignment-${data.studentId}`,
        ...data,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping));

      jest.spyOn(studentTutorMappingRepository, 'save').mockImplementation((assignment: any) =>
        Promise.resolve(assignment)
      );

      // Measure performance
      const startTime = Date.now();

      const result = await service.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockFeature.id,
        studentIds: studentIds,
        reassignExisting: false
      });

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Verify results
      expect(result).toHaveLength(100);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Verify tutor distribution (should be relatively balanced)
      const tutorAssignmentCounts = new Map<string, number>();
      result.forEach(assignment => {
        const count = tutorAssignmentCounts.get(assignment.tutorId) || 0;
        tutorAssignmentCounts.set(assignment.tutorId, count + 1);
      });

      // Each tutor should have roughly 10 students (100 students / 10 tutors)
      const assignmentCounts = Array.from(tutorAssignmentCounts.values());
      const avgAssignments = assignmentCounts.reduce((a, b) => a + b, 0) / assignmentCounts.length;
      expect(avgAssignments).toBeCloseTo(10, 1); // Within 1 assignment of average
    });

    it('should efficiently handle students with existing assignments', async () => {
      // Test with 50 students who already have assignments
      const students = generateMockStudents(50);
      const tutors = generateMockTutors(5);
      const studentIds = students.map(s => s.id);

      // Mock existing assignments for all students
      const existingAssignments = students.map((student, index) => ({
        id: `existing-${student.id}`,
        studentId: student.id,
        tutorId: tutors[index % tutors.length].id, // Distribute across tutors
        planFeatureId: 'other-feature',
        status: MappingStatus.ACTIVE,
        assignedDate: new Date('2024-01-01'),
        tutor: tutors[index % tutors.length],
        planFeature: mockFeature
      } as StudentTutorMapping));

      jest.spyOn(studentTutorMappingRepository, 'find').mockImplementation((options: any) => {
        const studentId = options.where.studentId;
        return Promise.resolve(existingAssignments.filter(a => a.studentId === studentId));
      });

      // Mock no existing assignments for the new feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Mock assignment creation with existing tutors
      jest.spyOn(studentTutorMappingRepository, 'create').mockImplementation((data: any) => {
        const existingAssignment = existingAssignments.find(a => a.studentId === data.studentId);
        return {
          id: `new-assignment-${data.studentId}`,
          ...data,
          tutorId: existingAssignment?.tutorId || data.tutorId,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          notes: 'Auto-assigned by system with preferred tutor logic'
        } as StudentTutorMapping;
      });

      jest.spyOn(studentTutorMappingRepository, 'save').mockImplementation((assignment: any) =>
        Promise.resolve(assignment)
      );

      // Measure performance
      const startTime = Date.now();

      const result = await service.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockFeature.id,
        studentIds: studentIds,
        reassignExisting: false
      });

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Verify results
      expect(result).toHaveLength(50);
      expect(executionTime).toBeLessThan(3000); // Should be faster with existing assignments

      // Verify tutor consistency - each student should get their existing tutor
      result.forEach((assignment, index) => {
        const expectedTutorId = tutors[index % tutors.length].id;
        expect(assignment.tutorId).toBe(expectedTutorId);
      });
    });
  });

  describe('Concurrent Assignment Handling', () => {
    it('should handle concurrent assignment requests without conflicts', async () => {
      // Simulate concurrent requests for the same students
      const students = generateMockStudents(20);
      const tutors = generateMockTutors(3);
      const studentIds = students.map(s => s.id);

      // Mock setup
      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([]);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(tutors),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
      jest.spyOn(studentTutorMappingRepository, 'count').mockResolvedValue(0);
      jest.spyOn(userRepository, 'findOne').mockImplementation((options: any) => {
        const studentId = options.where.id;
        return Promise.resolve(students.find(s => s.id === studentId));
      });
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Mock assignment creation with slight delay to simulate real conditions
      jest.spyOn(studentTutorMappingRepository, 'create').mockImplementation((data: any) => ({
        id: `assignment-${data.studentId}-${Date.now()}`,
        ...data,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping));

      jest.spyOn(studentTutorMappingRepository, 'save').mockImplementation(async (assignment: any) => {
        // Simulate database save delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
        return assignment;
      });

      // Execute concurrent requests
      const concurrentRequests = Array.from({ length: 3 }, (_, i) =>
        service.autoAssignTutorsWithoutNotifications({
          planFeatureId: `feature-${i + 1}`,
          studentIds: studentIds.slice(i * 7, (i + 1) * 7), // Different student subsets
          reassignExisting: false
        })
      );

      const results = await Promise.all(concurrentRequests);

      // Verify all requests completed successfully
      expect(results).toHaveLength(3);
      expect(results[0]).toHaveLength(7);
      expect(results[1]).toHaveLength(7);
      expect(results[2]).toHaveLength(6); // Last subset has 6 students

      // Verify no duplicate assignments
      const allAssignments = results.flat();
      const assignmentIds = allAssignments.map(a => a.id);
      const uniqueAssignmentIds = new Set(assignmentIds);
      expect(uniqueAssignmentIds.size).toBe(assignmentIds.length);
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should not cause memory leaks with large datasets', async () => {
      // Test with very large dataset to check for memory leaks
      const students = generateMockStudents(500);
      const tutors = generateMockTutors(20);

      // Mock efficient database operations
      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue([]);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(tutors),
      };
      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Mock balanced workload distribution
      jest.spyOn(studentTutorMappingRepository, 'count').mockImplementation((options: any) => {
        const tutorId = options.where.tutorId;
        const tutorIndex = tutors.findIndex(t => t.id === tutorId);
        return Promise.resolve(tutorIndex * 5); // Simulate workload
      });

      jest.spyOn(userRepository, 'findOne').mockImplementation((options: any) => {
        const studentId = options.where.id;
        return Promise.resolve(students.find(s => s.id === studentId));
      });

      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Mock efficient assignment creation
      jest.spyOn(studentTutorMappingRepository, 'create').mockImplementation((data: any) => ({
        id: `assignment-${data.studentId}`,
        ...data,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      } as StudentTutorMapping));

      jest.spyOn(studentTutorMappingRepository, 'save').mockImplementation((assignment: any) =>
        Promise.resolve(assignment)
      );

      // Measure memory usage (approximate)
      const initialMemory = process.memoryUsage().heapUsed;

      // Process in batches to simulate real-world usage
      const batchSize = 100;
      const batches = [];
      for (let i = 0; i < students.length; i += batchSize) {
        batches.push(students.slice(i, i + batchSize).map(s => s.id));
      }

      const results = [];
      for (const batch of batches) {
        const batchResult = await service.autoAssignTutorsWithoutNotifications({
          planFeatureId: mockFeature.id,
          studentIds: batch,
          reassignExisting: false
        });
        results.push(...batchResult);
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Verify results
      expect(results).toHaveLength(500);

      // Memory increase should be reasonable (less than 50MB for 500 assignments)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);

      // Verify tutor distribution is balanced
      const tutorCounts = new Map<string, number>();
      results.forEach(assignment => {
        const count = tutorCounts.get(assignment.tutorId) || 0;
        tutorCounts.set(assignment.tutorId, count + 1);
      });

      const counts = Array.from(tutorCounts.values());
      const maxCount = Math.max(...counts);
      const minCount = Math.min(...counts);
      const difference = maxCount - minCount;

      // Distribution should be relatively balanced (difference < 10)
      expect(difference).toBeLessThan(10);
    });
  });
});
