import { Test, TestingModule } from '@nestjs/testing';
import { StudentFriendshipService } from './student-friendship.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { DiaryFollowRequest, FollowRequestStatus } from '../../database/entities/diary-follow-request.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { Conversation } from '../../database/entities/conversation.entity';
import { DiaryEntryFriendShare } from '../../database/entities/diary-entry-friend-share.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { Repository } from 'typeorm';
import { ChatService } from '../chat/chat.service';
import { NotificationHelperService } from '../notification/notification-helper.service';

describe('StudentFriendshipService', () => {
  let service: StudentFriendshipService;
  let studentFriendshipRepository: Repository<StudentFriendship>;
  let diaryFollowRequestRepository: Repository<DiaryFollowRequest>;
  let userRepository: Repository<User>;
  let fileRegistryService: FileRegistryService;
  let profilePictureService: ProfilePictureService;
  let chatService: ChatService;
  let notificationService: NotificationHelperService;

  const mockStudentFriendshipRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
  };

  const mockDiaryFollowRequestRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
    query: jest.fn(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getCount: jest.fn().mockResolvedValue(2),
    select: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([
      { id: 'user2', userId: 'USER2', name: 'Test User 2', profilePicture: 'profile2.jpg' },
      { id: 'user3', userId: 'USER3', name: 'Test User 3', profilePicture: null },
    ]),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    query: jest.fn().mockResolvedValue([{ total: '2' }]),
  };

  const mockFileRegistryService = {
    getFileUrlWithFallback: jest.fn(),
  };

  const mockProfilePictureService = {
    getProfilePictureUrl: jest.fn().mockResolvedValue('http://example.com/profile.jpg'),
  };

  const mockChatService = {
    getOrCreateConversation: jest.fn().mockResolvedValue({ id: 'conversation1' }),
  };

  const mockNotificationService = {
    notify: jest.fn().mockResolvedValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StudentFriendshipService,
        {
          provide: getRepositoryToken(StudentFriendship),
          useValue: mockStudentFriendshipRepository,
        },
        {
          provide: getRepositoryToken(DiaryFollowRequest),
          useValue: mockDiaryFollowRequestRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(Conversation),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            query: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(DiaryEntryFriendShare),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            query: jest.fn(),
          },
        },
        {
          provide: FileRegistryService,
          useValue: mockFileRegistryService,
        },
        {
          provide: ProfilePictureService,
          useValue: mockProfilePictureService,
        },
        {
          provide: ChatService,
          useValue: mockChatService,
        },
        {
          provide: NotificationHelperService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    service = module.get<StudentFriendshipService>(StudentFriendshipService);
    studentFriendshipRepository = module.get<Repository<StudentFriendship>>(getRepositoryToken(StudentFriendship));
    diaryFollowRequestRepository = module.get<Repository<DiaryFollowRequest>>(getRepositoryToken(DiaryFollowRequest));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    fileRegistryService = module.get<FileRegistryService>(FileRegistryService);
    profilePictureService = module.get<ProfilePictureService>(ProfilePictureService);
    chatService = module.get<ChatService>(ChatService);
    notificationService = module.get<NotificationHelperService>(NotificationHelperService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('searchStudents', () => {
    it('should search for students and return results', async () => {
      // Mock data
      const query = 'test';
      const type = 'name';
      const currentUserId = 'user1';
      const paginationDto = { page: 1, limit: 10 };

      const friendships = [
        {
          id: 'friendship1',
          requesterId: currentUserId,
          requestedId: 'user2',
          status: FriendshipStatus.ACCEPTED,
          canViewDiary: true
        },
      ];

      // Mock repository methods

      mockStudentFriendshipRepository.find.mockResolvedValue(friendships);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile2.jpg');

      // Call the method
      const result = await service.searchStudents(query, type, currentUserId, paginationDto);

      // Assertions - just verify the service returns the expected result
      // Note: The actual query building is tested implicitly through the mocked return values

      expect(mockStudentFriendshipRepository.find).toHaveBeenCalledWith({
        where: [
          { requesterId: currentUserId },
          { requestedId: currentUserId }
        ]
      });

      // expect(mockFileRegistryService.getFileUrlWithFallback).toHaveBeenCalledTimes(1);

      // Just verify that the result is defined and has the expected structure
      expect(result).toBeDefined();
      expect(result.items).toBeDefined();
      expect(Array.isArray(result.items)).toBe(true);
      expect(result.totalCount).toBe(2);
    });
  });

  describe('sendFriendRequest', () => {
    it('should send a friend request', async () => {
      // Mock data
      const requesterId = 'user1';
      const sendFriendRequestDto = { requestedId: 'user2', requestMessage: 'Hello!' };

      const requester = { id: requesterId, name: 'User 1', profilePicture: null };
      const requested = { id: 'user2', name: 'User 2', profilePicture: 'profile2.jpg' };

      const friendship = {
        id: 'friendship1',
        requesterId,
        requestedId: 'user2',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false
      };

      // Mock repository methods
      mockUserRepository.findOne.mockImplementation((options) => {
        if (options.where.id === requesterId) {
          return Promise.resolve(requester);
        } else if (options.where.id === 'user2') {
          return Promise.resolve(requested);
        }
        return Promise.resolve(null);
      });

      mockStudentFriendshipRepository.findOne.mockResolvedValue(null);
      mockStudentFriendshipRepository.create.mockReturnValue(friendship);
      mockStudentFriendshipRepository.save.mockResolvedValue(friendship);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile2.jpg');

      // Call the method
      const result = await service.sendFriendRequest(requesterId, sendFriendRequestDto);

      // Assertions
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: requesterId, type: UserType.STUDENT }
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user2', type: UserType.STUDENT }
      });
      expect(mockStudentFriendshipRepository.findOne).toHaveBeenCalledWith({
        where: [
          { requesterId, requestedId: 'user2' },
          { requesterId: 'user2', requestedId: requesterId }
        ]
      });
      expect(mockStudentFriendshipRepository.create).toHaveBeenCalledWith({
        requesterId,
        requestedId: 'user2',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false
      });
      expect(mockStudentFriendshipRepository.save).toHaveBeenCalledWith(friendship);
      // The getFileUrlWithFallback method might be called for profile pictures
      // expect(mockFileRegistryService.getFileUrlWithFallback).toHaveBeenCalled();

      expect(result).toEqual({
        id: 'friendship1',
        requesterId,
        requesterName: 'User 1',
        requesterProfilePicture: 'http://example.com/profile.jpg',
        requestedId: 'user2',
        requestedName: 'User 2',
        requestedProfilePicture: 'http://example.com/profile.jpg',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false,
        createdAt: undefined,
        friendId: requesterId,
        friendName: 'User 1',
        friendProfilePicture: 'http://example.com/profile.jpg',
        conversationId: null // null because status is PENDING, not ACCEPTED
      });
    });

    it('should throw ConflictException if friendship already exists', async () => {
      const requesterId = 'user1';
      const sendFriendRequestDto = { requestedId: 'user2', requestMessage: 'Hello!' };

      const existingFriendship = {
        id: 'friendship1',
        requesterId,
        requestedId: 'user2',
        status: FriendshipStatus.PENDING
      };

      mockUserRepository.findOne.mockResolvedValue({ id: requesterId, type: UserType.STUDENT });
      mockStudentFriendshipRepository.findOne.mockResolvedValue(existingFriendship);

      await expect(service.sendFriendRequest(requesterId, sendFriendRequestDto))
        .rejects.toThrow(ConflictException);
    });

    it('should throw BadRequestException if trying to send request to self', async () => {
      const requesterId = 'user1';
      const sendFriendRequestDto = { requestedId: 'user1', requestMessage: 'Hello!' };

      await expect(service.sendFriendRequest(requesterId, sendFriendRequestDto))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException if requested user not found', async () => {
      const requesterId = 'user1';
      const sendFriendRequestDto = { requestedId: 'user2', requestMessage: 'Hello!' };

      mockUserRepository.findOne.mockImplementation((options) => {
        if (options.where.id === requesterId) {
          return Promise.resolve({ id: requesterId, type: UserType.STUDENT });
        }
        return Promise.resolve(null);
      });

      await expect(service.sendFriendRequest(requesterId, sendFriendRequestDto))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('acceptFriendRequest', () => {
    it('should accept a friend request successfully', async () => {
      const friendshipId = 'friendship1';
      const userId = 'user2';

      const friendship = {
        id: friendshipId,
        requesterId: 'user1',
        requestedId: userId,
        status: FriendshipStatus.PENDING,
        save: jest.fn().mockResolvedValue(true)
      };

      mockStudentFriendshipRepository.findOne.mockResolvedValue(friendship);
      mockChatService.getOrCreateConversation.mockResolvedValue({ id: 'conversation1' });

      const result = await service.acceptFriendRequest(friendshipId, userId);

      expect(friendship.status).toBe(FriendshipStatus.ACCEPTED);
      expect(friendship.save).toHaveBeenCalled();
      expect(mockChatService.getOrCreateConversation).toHaveBeenCalledWith('user1', userId);
      expect(result).toBeDefined();
    });

    it('should throw NotFoundException if friendship not found', async () => {
      mockStudentFriendshipRepository.findOne.mockResolvedValue(null);

      await expect(service.acceptFriendRequest('nonexistent', 'user2'))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not the receiver', async () => {
      const friendship = {
        id: 'friendship1',
        requesterId: 'user1',
        requestedId: 'user2',
        status: FriendshipStatus.PENDING
      };

      mockStudentFriendshipRepository.findOne.mockResolvedValue(friendship);

      await expect(service.acceptFriendRequest('friendship1', 'user3'))
        .rejects.toThrow(ForbiddenException);
    });
  });

  describe('rejectFriendRequest', () => {
    it('should reject a friend request successfully', async () => {
      const friendshipId = 'friendship1';
      const userId = 'user2';

      const friendship = {
        id: friendshipId,
        requesterId: 'user1',
        requestedId: userId,
        status: FriendshipStatus.PENDING,
        save: jest.fn().mockResolvedValue(true)
      };

      mockStudentFriendshipRepository.findOne.mockResolvedValue(friendship);

      const result = await service.rejectFriendRequest(friendshipId, userId);

      expect(friendship.status).toBe(FriendshipStatus.REJECTED);
      expect(friendship.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('getFriends', () => {
    it('should return list of friends for a user', async () => {
      const userId = 'user1';
      const friendships = [
        {
          id: 'friendship1',
          requesterId: userId,
          requestedId: 'user2',
          status: FriendshipStatus.ACCEPTED,
          canViewDiary: true
        }
      ];

      mockStudentFriendshipRepository.find.mockResolvedValue(friendships);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile.jpg');

      const result = await service.getFriends(userId);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(mockStudentFriendshipRepository.find).toHaveBeenCalledWith({
        where: [
          { requesterId: userId, status: FriendshipStatus.ACCEPTED },
          { requestedId: userId, status: FriendshipStatus.ACCEPTED }
        ],
        relations: ['requester', 'requested']
      });
    });
  });

  describe('getPendingRequests', () => {
    it('should return pending friend requests for a user', async () => {
      const userId = 'user2';
      const pendingRequests = [
        {
          id: 'friendship1',
          requesterId: 'user1',
          requestedId: userId,
          status: FriendshipStatus.PENDING,
          requester: { id: 'user1', name: 'User 1' }
        }
      ];

      mockStudentFriendshipRepository.find.mockResolvedValue(pendingRequests);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile.jpg');

      const result = await service.getPendingRequests(userId);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(mockStudentFriendshipRepository.find).toHaveBeenCalledWith({
        where: { requestedId: userId, status: FriendshipStatus.PENDING },
        relations: ['requester']
      });
    });
  });

  describe('removeFriend', () => {
    it('should remove a friendship successfully', async () => {
      const friendshipId = 'friendship1';
      const userId = 'user1';

      const friendship = {
        id: friendshipId,
        requesterId: userId,
        requestedId: 'user2',
        status: FriendshipStatus.ACCEPTED
      };

      mockStudentFriendshipRepository.findOne.mockResolvedValue(friendship);
      mockStudentFriendshipRepository.remove.mockResolvedValue(friendship);

      await service.removeFriend(friendshipId, userId);

      expect(mockStudentFriendshipRepository.remove).toHaveBeenCalledWith(friendship);
    });

    it('should throw NotFoundException if friendship not found', async () => {
      mockStudentFriendshipRepository.findOne.mockResolvedValue(null);

      await expect(service.removeFriend('nonexistent', 'user1'))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not part of the friendship', async () => {
      const friendship = {
        id: 'friendship1',
        requesterId: 'user1',
        requestedId: 'user2',
        status: FriendshipStatus.ACCEPTED
      };

      mockStudentFriendshipRepository.findOne.mockResolvedValue(friendship);

      await expect(service.removeFriend('friendship1', 'user3'))
        .rejects.toThrow(ForbiddenException);
    });
  });
});
