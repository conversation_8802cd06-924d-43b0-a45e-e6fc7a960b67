import { Test, TestingModule } from '@nestjs/testing';
import { TutorMatchingService } from '../src/modules/tutor-matching/tutor-matching.service';
import { PlansService } from '../src/modules/plans/plans.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentTutorMapping, MappingStatus } from '../src/database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../src/database/entities/user.entity';
import { PlanFeature } from '../src/database/entities/plan-feature.entity';

describe('Tutor Assignment Consistency', () => {
  let tutorMatchingService: TutorMatchingService;
  let plansService: PlansService;
  let studentTutorMappingRepository: Repository<StudentTutorMapping>;
  let userRepository: Repository<User>;
  let planFeatureRepository: Repository<PlanFeature>;

  // Mock data
  const mockStudent = {
    id: 'student-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    type: UserType.STUDENT
  };

  const mockTutor1 = {
    id: 'tutor-1',
    name: 'Jane Smith',
    email: '<EMAIL>',
    type: UserType.TUTOR
  };

  const mockTutor2 = {
    id: 'tutor-2',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    type: UserType.TUTOR
  };

  const mockFeature1 = {
    id: 'feature-1',
    name: 'English Writing',
    type: 'MODULE'
  };

  const mockFeature2 = {
    id: 'feature-2',
    name: 'English Speaking',
    type: 'MODULE'
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TutorMatchingService,
        PlansService,
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            count: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
          },
        },
        // Add other required dependencies as mocks
      ],
    }).compile();

    tutorMatchingService = module.get<TutorMatchingService>(TutorMatchingService);
    plansService = module.get<PlansService>(PlansService);
    studentTutorMappingRepository = module.get<Repository<StudentTutorMapping>>(
      getRepositoryToken(StudentTutorMapping),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    planFeatureRepository = module.get<Repository<PlanFeature>>(getRepositoryToken(PlanFeature));
  });

  describe('getStudentTutorAssignments', () => {
    it('should return all active assignments for a student ordered by assignment date', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        },
        {
          id: 'assignment-2',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature2.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-02'),
          tutor: mockTutor1,
          planFeature: mockFeature2
        }
      ];

      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue(mockAssignments);

      const result = await tutorMatchingService.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual(mockAssignments);
      expect(studentTutorMappingRepository.find).toHaveBeenCalledWith({
        where: {
          studentId: mockStudent.id,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor', 'planFeature'],
        order: { assignedDate: 'ASC' }
      });
    });

    it('should return empty array on error', async () => {
      jest.spyOn(studentTutorMappingRepository, 'find').mockRejectedValue(new Error('Database error'));

      const result = await tutorMatchingService.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual([]);
    });
  });

  describe('getOrSelectPreferredTutor', () => {
    it('should return existing preferred tutor for student with assignments', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        }
      ];

      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue(mockAssignments);

      const result = await tutorMatchingService.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
    });

    it('should select new tutor for student with no assignments', async () => {
      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([]);
      jest.spyOn(tutorMatchingService, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);

      const result = await tutorMatchingService.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
      expect(tutorMatchingService.selectTutorByOverallWorkload).toHaveBeenCalled();
    });
  });

  describe('Consistent Assignment Scenarios', () => {
    it('should assign same tutor to multiple features for new student', async () => {
      // Mock new student with no existing assignments
      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([]);
      jest.spyOn(tutorMatchingService, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);
      
      // Mock feature validation
      jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature1);
      
      // Mock student validation
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);
      
      // Mock no existing assignment for the feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);
      
      // Mock assignment creation
      const mockMapping = {
        id: 'assignment-1',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id,
        planFeatureId: mockFeature1.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      };
      jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping);
      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping);

      // Test assignment for first feature
      const assignments1 = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockFeature1.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Verify first assignment uses selected tutor
      expect(assignments1).toHaveLength(1);
      expect(assignments1[0].tutorId).toBe(mockTutor1.id);

      // Mock that student now has the first assignment
      const existingAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          tutor: mockTutor1,
          planFeature: mockFeature1
        }
      ];
      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue(existingAssignments);

      // Mock second feature validation
      jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature2);
      
      // Mock no existing assignment for the second feature
      jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

      // Mock assignment creation for second feature
      const mockMapping2 = {
        id: 'assignment-2',
        studentId: mockStudent.id,
        tutorId: mockTutor1.id, // Same tutor
        planFeatureId: mockFeature2.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        notes: 'Auto-assigned by system with preferred tutor logic'
      };
      jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping2);
      jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping2);

      // Test assignment for second feature
      const assignments2 = await tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: mockFeature2.id,
        studentIds: [mockStudent.id],
        reassignExisting: false
      });

      // Verify second assignment uses same tutor
      expect(assignments2).toHaveLength(1);
      expect(assignments2[0].tutorId).toBe(mockTutor1.id);
      
      // Both assignments should have the same tutor
      expect(assignments1[0].tutorId).toBe(assignments2[0].tutorId);
    });
  });
});
