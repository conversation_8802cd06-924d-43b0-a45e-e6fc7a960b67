import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { QAQuestion } from './qa-question.entity';
import { QASubmission } from './qa-submission.entity';

export enum QAAssignmentStatus {
  ASSIGNED = 'assigned',
  DRAFT = 'draft',
  //PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  EXPIRED = 'expired'
}

@Entity()
export class QAAssignment extends AuditableBaseEntity {
  @Column({ name: 'question_id' })
  questionId: string;

  @ManyToOne(() => QAQuestion)
  @JoinColumn({ name: 'question_id' })
  question: QAQuestion;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  // @Column({ name: 'tutor_id' })
  // tutorId: string;

  // @ManyToOne(() => User)
  // @JoinColumn({ name: 'tutor_id' })
  // tutor: User;

  @Column({ nullable: true })
  points: number;

  @Column({ type: 'timestamp', nullable: true })
  deadline?: Date;

  @Column({ type: 'text', nullable: true })
  instructions: string;

  @Column({
    type: 'enum',
    enum: QAAssignmentStatus,
    default: QAAssignmentStatus.ASSIGNED
  })
  status: QAAssignmentStatus;

  @Column({ name: 'assigned_date', type: 'timestamp' })
  assignedDate: Date;

  @OneToOne(() => QASubmission, submission => submission.assignment)
  submission: QASubmission;
}