import { Injectable, ConflictException, NotFoundException, BadRequestException, Inject, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';
import { CreateUserDto, UserResponseDto, CreateAdminUserDto, CreateTutorUserDto, CreateStudentUserDto, UpdateProfileDto, CalculateAgeResponseDto, CreateAdminUserByAdminDto, CreateTutorUserByAdminDto, CreateStudentUserByAdminDto, UserCreationResponseDto } from '../../database/models/users.dto';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { Role } from '../../database/entities/role.entity';
import { UserRole } from '../../database/entities/user-role.entity';
import { Diary } from '../../database/entities/diary.entity';
import { generateSecurePassword } from '../../common/utils/password-generator.util';
import { EmailService } from '../email/email.service';
import { PaginationDto } from '../../common/models/pagination.dto';
import { calculateAge, getCurrentUTCDate, formatToYYYYMMDD } from '../../common/utils/date-utils';
import { trimUserId } from '../../common/utils/user-utils';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(TutorApproval)
    private readonly tutorApprovalRepository: Repository<TutorApproval>,
    @Inject(ProfilePictureService)
    private readonly profilePictureService: ProfilePictureService,
    private readonly dataSource: DataSource,
    private readonly emailService: EmailService
  ) {}

  async findByEmail(email: string): Promise<User | undefined> {
    const user = await this.userRepository.findOne({
      where: {
        email: email
      },
      relations: ['userRoles', 'userRoles.role', 'userPlans', 'userPlans.plan']
    });
    return user || undefined;
  }

  async findByUserId(userId: string): Promise<User | undefined> {
    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userId);

    const user = await this.userRepository.findOne({
      where: {
        userId: trimmedUserId
      },
      relations: ['userRoles', 'userRoles.role', 'userPlans', 'userPlans.plan']
    });
    return user || undefined;
  }

  /**
   * Find a user by ID with all relations loaded
   * @param id The ID of the user to find
   * @param loadRelations Whether to load all relations (default: true)
   * @returns The user entity or undefined if not found
   */
  async findById(id: string, loadRelations: boolean = true): Promise<User | undefined> {
    // Define the relations to load
    const relations = loadRelations ? [
      'userRoles',
      'userRoles.role',
      'userPlans',
      'userPlans.plan',
      'userPlans.plan.planFeatures',
      'profilePictureEntity'
    ] : ['userRoles', 'userRoles.role'];

    const user = await this.userRepository.findOne({
      where: {
        id: id
      },
      relations: relations
    });

    if (user) {
      // Check if the user has a profile picture
      const hasProfilePicture = await this.profilePictureService.hasProfilePicture(id);

      // Add profile picture URL to the user object for later use only if a profile picture exists.
      // This will be used when converting to DTO
      if (hasProfilePicture) {
        user.profilePicture = await this.profilePictureService.getProfilePictureUrl(id);
        user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(id);
      } else {
        user.profilePicture = null;
        user['_profilePictureUrl'] = null;
      }

      // For student users, try to load additional student-specific data
      if (user.type === UserType.STUDENT) {
        try {
          // Set default skin ID from user entity (use diary skin as primary default for backward compatibility)
          user['_defaultSkinId'] = user.defaultDiarySkinId || null;

          // Load assigned tutors
          const assignedTutorsQuery = await this.studentTutorMappingRepository.find({
            where: { studentId: id, status: MappingStatus.ACTIVE },
            relations: ['tutor', 'planFeature']
          });

          if (assignedTutorsQuery && assignedTutorsQuery.length > 0) {
            user['_assignedTutors'] = assignedTutorsQuery.map(mapping => ({
              id: mapping.id,
              tutorId: mapping.tutorId,
              tutorName: mapping.tutor?.name || 'Unknown',
              moduleId: mapping.planFeatureId,
              moduleName: mapping.planFeature?.name || 'Unknown'
            }));
          }
        } catch (error) {
          console.error(`Error loading additional student data for user ${id}:`, error);
        }
      }

      // For tutor users, try to load additional tutor-specific data
      if (user.type === UserType.TUTOR) {
        try {
          // Load assigned students
          const assignedStudentsQuery = await this.studentTutorMappingRepository.find({
            where: { tutorId: id, status: MappingStatus.ACTIVE },
            relations: ['student', 'planFeature']
          });

          if (assignedStudentsQuery && assignedStudentsQuery.length > 0) {
            user['_assignedStudents'] = assignedStudentsQuery.map(mapping => ({
              id: mapping.id,
              studentId: mapping.studentId,
              studentName: mapping.student?.name || 'Unknown',
              moduleId: mapping.planFeatureId,
              moduleName: mapping.planFeature?.name || 'Unknown'
            }));

            // Get unique modules this tutor is assigned to
            const uniqueModules = [...new Set(assignedStudentsQuery.map(mapping => mapping.planFeatureId))];
            user['_assignedModules'] = uniqueModules.map(moduleId => {
              const module = assignedStudentsQuery.find(mapping => mapping.planFeatureId === moduleId)?.planFeature;
              return {
                id: moduleId,
                name: module?.name || 'Unknown'
              };
            });
          }

          // Load education information
          try {
            const educationQuery = await this.dataSource.query(
              `SELECT
                id, tutor_id, degree, institution, field_of_study,
                start_date, end_date, is_current, description,
                location, grade, activities, created_at, updated_at
              FROM tutor_education
              WHERE tutor_id = $1
              ORDER BY start_date DESC`,
              [id]
            );

            if (educationQuery && educationQuery.length > 0) {
              user['_education'] = educationQuery.map(edu => ({
                id: edu.id,
                tutorId: edu.tutor_id,
                degree: edu.degree,
                institution: edu.institution,
                fieldOfStudy: edu.field_of_study,
                startDate: edu.start_date ? formatToYYYYMMDD(new Date(edu.start_date)) : null,
                endDate: edu.end_date ? formatToYYYYMMDD(new Date(edu.end_date)) : null,
                isCurrent: edu.is_current,
                description: edu.description,
                location: edu.location,
                grade: edu.grade,
                activities: edu.activities,
                createdAt: edu.created_at,
                updatedAt: edu.updated_at
              }));
            } else {
              user['_education'] = [];
            }
          } catch (error) {
            console.error(`Error loading education data for tutor ${id}:`, error);
            user['_education'] = [];
          }
        } catch (error) {
          console.error(`Error loading additional tutor data for user ${id}:`, error);
        }
      }
    }

    return user || undefined;
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<UserResponseDto> {
    // Find the user by ID with all relations loaded, including education information for tutors
    const user = await this.findById(userId, true);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Store the original roles to ensure they're preserved
    const originalUserRoles = user.userRoles ? [...user.userRoles] : [];

    // Create a copy of the DTO to avoid modifying the original
    const profileToUpdate = { ...updateProfileDto };

    // Name is no longer unique, so we don't need to check for name uniqueness

    // We don't need to explicitly delete userId and email from profileToUpdate
    // since they're not included in the UpdateProfileDto

    // Convert dateOfBirth from string to Date if provided
    if (profileToUpdate.dateOfBirth) {
      try {
        // Parse the YYYY-MM-DD format
        const [year, month, day] = profileToUpdate.dateOfBirth.split('-').map(Number);
        // Month is 0-indexed in JavaScript Date
        const dateObj = new Date(year, month - 1, day);

        // Assign the Date object to the user's dateOfBirth directly
        user.dateOfBirth = dateObj;

        // Remove dateOfBirth from profileToUpdate to avoid type conflicts
        delete profileToUpdate.dateOfBirth;
      } catch (error) {
        throw new BadRequestException('Invalid date format. Date of birth must be in YYYY-MM-DD format.');
      }
    }

    // Update only the profile fields, not affecting relationships
    // Instead of using Object.assign which might affect relationships,
    // we'll update each field individually
    Object.keys(profileToUpdate).forEach((key) => {
      user[key] = profileToUpdate[key];
    });

    // Ensure roles are preserved
    user.userRoles = originalUserRoles;

    // Save the updated user
    const updatedUser = await this.userRepository.save(user);

    // Double-check that roles are still intact
    const userWithRoles = await this.findById(userId, true);
    if (!userWithRoles.userRoles || userWithRoles.userRoles.length === 0) {
      console.warn(`User ${userId} lost roles during profile update. Attempting to restore...`);

      // If roles were lost, restore them
      for (const userRole of originalUserRoles) {
        // Check if the role still exists in the database
        const existingUserRole = await this.userRoleRepository.findOne({
          where: {
            userId: userId,
            roleId: userRole.roleId
          }
        });

        if (!existingUserRole) {
          // Recreate the user role
          const newUserRole = this.userRoleRepository.create({
            userId: userId,
            roleId: userRole.roleId
          });
          await this.userRoleRepository.save(newUserRole);
          console.log(`Restored role ${userRole.roleId} for user ${userId}`);
        }
      }

      // Refresh user with restored roles and all relations, including education information for tutors
      return (await this.findById(userId, true)).toDto();
    }

    // Load the user again with all relations to ensure education information is included for tutors
    const userWithAllRelations = await this.findById(userId, true);
    return userWithAllRelations.toDto();
  }

  async updateProfilePicture(userId: string, file: any): Promise<UserResponseDto> {
    // Load the user with all relations, including education information for tutors
    const user = await this.findById(userId, true);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Upload the profile picture using the ProfilePictureService
    // This will handle deleting the old picture if it exists
    await this.profilePictureService.uploadProfilePicture(file, userId);

    // Get the updated user with all relations, including education information for tutors
    const updatedUser = await this.findById(userId, true);
    if (!updatedUser) {
      throw new NotFoundException('User not found after updating profile picture');
    }

    // Get the user DTO
    const userDto = updatedUser.toDto();

    // Use the profile picture registry service to get the URL
    userDto.profilePictureUrl = await this.profilePictureService.getProfilePictureUrl(userId);

    return userDto;
  }

  /**
   * Calculate age from date of birth
   * @param dateOfBirth Date of birth in YYYY-MM-DD format
   * @returns Calculated age
   */
  calculateAge(dateOfBirth: string): CalculateAgeResponseDto {
    // Validate the date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateOfBirth)) {
      throw new BadRequestException('Date of birth must be in YYYY-MM-DD format (e.g., 1990-01-01)');
    }

    const age = calculateAge(dateOfBirth);

    if (age === null) {
      throw new BadRequestException('Invalid date of birth');
    }

    return { age };
  }

  async createUser(userData: CreateUserDto): Promise<UserResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    // Check if userId already exists
    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Update the userData with the trimmed userId
    userData.userId = trimmedUserId;
    if (userData.name === userData.userId) {
      userData.name = trimmedUserId; // Also update name if it's the same as userId
    }

    // Create user with provided properties
    const userDataCamelCase = {
      name: userData.name,
      userId: userData.userId,
      email: userData.email,
      password: userData.password,
      phoneNumber: userData.phoneNumber,
      gender: userData.gender,
      agreedToTerms: userData.agreedToTerms
    };

    const newUser = this.userRepository.create(userDataCamelCase);
    const savedUser = await this.userRepository.save(newUser);
    return savedUser.toDto();
  }

  async createAdminUser(userData: CreateAdminUserDto): Promise<UserResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    // Check if userId already exists
    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Create user with admin type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: userData.phoneNumber,
      gender: userData.gender,
      agreedToTerms: userData.agreedToTerms || true,
      type: UserType.ADMIN,
      isConfirmed: true, // Admin users are auto-confirmed
      isActive: true
    });

    // Set and hash the password
    newUser.setPassword(userData.password);

    const savedUser = await this.userRepository.save(newUser);

    // Find admin role
    let adminRole = await this.roleRepository.findOne({ where: { name: 'admin' } });

    // Create admin role if it doesn't exist
    if (!adminRole) {
      adminRole = this.roleRepository.create({ name: 'admin' });
      adminRole = await this.roleRepository.save(adminRole);
    }

    // Assign admin role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: adminRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
      // Don't include user and role objects to prevent TypeORM from trying to save them
    });

    await this.userRoleRepository.save(userRole);

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }
    return userWithRoles.toDto();
  }

  async createTutorUser(userData: CreateTutorUserDto, skipVerification: boolean = false): Promise<UserResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Create user with tutor type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: userData.phoneNumber,
      gender: userData.gender,
      bio: userData.bio,
      agreedToTerms: userData.agreedToTerms || false,
      type: UserType.TUTOR,
      isConfirmed: skipVerification, // Only auto-confirm if skipVerification is true
      isActive: skipVerification // Only activate if skipVerification is true (admin-created tutors)
    });

    // Set and hash the password
    newUser.setPassword(userData.password);

    const savedUser = await this.userRepository.save(newUser);

    // Find tutor role
    let tutorRole = await this.roleRepository.findOne({ where: { name: 'tutor' } });

    // Create tutor role if it doesn't exist
    if (!tutorRole) {
      tutorRole = this.roleRepository.create({ name: 'tutor' });
      tutorRole = await this.roleRepository.save(tutorRole);
    }

    // Assign tutor role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: tutorRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
      // Don't include user and role objects to prevent TypeORM from trying to save them
    });

    await this.userRoleRepository.save(userRole);

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }
    return userWithRoles.toDto();
  }

  async createStudentUser(userData: CreateStudentUserDto, skipVerification: boolean = false): Promise<UserResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Create user with student type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: userData.phoneNumber,
      gender: userData.gender,
      agreedToTerms: userData.agreedToTerms || false,
      type: UserType.STUDENT,
      isConfirmed: skipVerification, // Only auto-confirm if skipVerification is true
      isActive: true
    });

    // Set and hash the password
    newUser.setPassword(userData.password);

    const savedUser = await this.userRepository.save(newUser);

    // Find student role
    let studentRole = await this.roleRepository.findOne({ where: { name: 'student' } });

    // Create student role if it doesn't exist
    if (!studentRole) {
      studentRole = this.roleRepository.create({ name: 'student' });
      studentRole = await this.roleRepository.save(studentRole);
    }

    // Assign student role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: studentRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
      // Don't include user and role objects to prevent TypeORM from trying to save them
    });

    await this.userRoleRepository.save(userRole);

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }
    return userWithRoles.toDto();
  }

  async assignRole(userId: string, roleName: string): Promise<UserResponseDto> {
    // Find user
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Find role
    let role = await this.roleRepository.findOne({ where: { name: roleName } });

    // Create role if it doesn't exist
    if (!role) {
      role = this.roleRepository.create({ name: roleName });
      role = await this.roleRepository.save(role);
    }

    // Check if user already has this role
    const existingUserRole = await this.userRoleRepository.findOne({
      where: {
        userId: userId,
        roleId: role.id
      }
    });

    if (existingUserRole) {
      throw new ConflictException(`User already has role ${roleName}`);
    }

    // Assign role to user
    const userRole = this.userRoleRepository.create({
      userId: userId,
      roleId: role.id

      // Don't include user and role objects to prevent TypeORM from trying to save them
    });

    await this.userRoleRepository.save(userRole);

    // Refresh user with roles
    const userWithRoles = await this.findById(userId);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }
    return userWithRoles.toDto();
  }

  async fixAllUserRoles(): Promise<any> {
    await this.fixAdminRoles();
    await this.fixTutorRoles();
    await this.fixStudentRoles();
    return { success: true };
  }

  private async assignRoleToUser(userId: string, roleName: string): Promise<void> {
    const role = await this.roleRepository.findOne({ where: { name: roleName } });
    if (!role) return;

    const existingUserRole = await this.userRoleRepository.findOne({
      where: { userId: userId, roleId: role.id }
    });

    if (!existingUserRole) {
      const userRole = this.userRoleRepository.create({
        userId: userId,
        roleId: role.id
      });
      await this.userRoleRepository.save(userRole);
    }
  }

  private async fixAdminRoles(): Promise<void> {
    const admins = await this.userRepository.find({
      where: { type: UserType.ADMIN },
      relations: ['userRoles', 'userRoles.role']
    });

    for (const admin of admins) {
      const hasAdminRole = admin.userRoles?.some(ur => ur.role?.name === 'admin');
      if (!hasAdminRole) {
        await this.assignRoleToUser(admin.id, 'admin');
      }
    }
  }

  private async fixTutorRoles(): Promise<void> {
    const tutors = await this.userRepository.find({
      where: { type: UserType.TUTOR },
      relations: ['userRoles', 'userRoles.role']
    });

    for (const tutor of tutors) {
      const hasTutorRole = tutor.userRoles?.some(ur => ur.role?.name === 'tutor');
      if (!hasTutorRole) {
        await this.assignRoleToUser(tutor.id, 'tutor');
      }
    }
  }

  private async fixStudentRoles(): Promise<void> {
    const students = await this.userRepository.find({
      where: { type: UserType.STUDENT },
      relations: ['userRoles', 'userRoles.role']
    });

    for (const student of students) {
      const hasStudentRole = student.userRoles?.some(ur => ur.role?.name === 'student');
      if (!hasStudentRole) {
        await this.assignRoleToUser(student.id, 'student');
      }
    }
  }

  async removeRole(userId: string, roleName: string): Promise<UserResponseDto> {
    // Find user
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Find role
    const role = await this.roleRepository.findOne({ where: { name: roleName } });
    if (!role) {
      throw new NotFoundException(`Role ${roleName} not found`);
    }

    // Check if user has this role
    const userRole = await this.userRoleRepository.findOne({
      where: {
        userId: userId,
        roleId: role.id
      }
    });

    if (!userRole) {
      throw new BadRequestException(`User does not have role ${roleName}`);
    }

    // Remove role from user
    await this.userRoleRepository.remove(userRole);

    // Refresh user with roles
    const userWithRoles = await this.findById(userId);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }
    return userWithRoles.toDto();
  }

  async findAll(): Promise<PagedListDto<UserResponseDto>> {
    const [users, totalCount] = await this.userRepository.findAndCount({
      where: { isActive: true },
      relations: ['userRoles', 'userRoles.role', 'userPlans', 'userPlans.plan']
    });

    // before returning, we need to check if the user has a profile picture and add the URL to the DTO
    for (const user of users) {
      user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(user.id);
    }

    return new PagedListDto(
      users.map((user) => user.toDto()),
      totalCount
    );
  }

  async findAllPaginated(filterDto: any): Promise<PagedListDto<UserResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', userId, email, phoneNumber, gender } = filterDto;

    const skip = (page - 1) * limit;

    // Use query builder instead of function expressions
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .leftJoinAndSelect('user.userPlans', 'userPlans')
      .leftJoinAndSelect('userPlans.plan', 'plan')
      .where('user.isActive = :isActive', { isActive: true });

    // Add filters if provided
    if (userId) {
      // Use partial matching only if userId has at least 3 characters
      if (userId.length >= 3) {
        queryBuilder.andWhere('LOWER(user.userId) LIKE LOWER(:userId)', { userId: `%${userId}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.userId) = LOWER(:userId)', { userId });
      }
    }

    if (email) {
      // Use partial matching only if email has at least 3 characters
      if (email.length >= 3) {
        queryBuilder.andWhere('LOWER(user.email) LIKE LOWER(:email)', { email: `%${email}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.email) = LOWER(:email)', { email });
      }
    }

    if (phoneNumber) {
      // Use partial matching only if phoneNumber has at least 3 characters
      if (phoneNumber.length >= 3) {
        queryBuilder.andWhere('user.phoneNumber LIKE :phoneNumber', { phoneNumber: `%${phoneNumber}%` });
      } else {
        queryBuilder.andWhere('user.phoneNumber = :phoneNumber', { phoneNumber });
      }
    }

    if (gender) {
      // Use partial matching only if gender has at least 3 characters
      if (gender.length >= 3) {
        queryBuilder.andWhere('LOWER(user.gender) LIKE LOWER(:gender)', { gender: `%${gender}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.gender) = LOWER(:gender)', { gender });
      }
    }

    // Validate sortBy field to prevent SQL injection
    const allowedSortFields = ['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Apply sorting
    queryBuilder.orderBy(`user.${validSortBy}`, sortDirection as 'ASC' | 'DESC');

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute the query
    const [users, totalCount] = await queryBuilder.getManyAndCount();

    // Add profile picture URLs and user-specific data
    for (const user of users) {
      user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(user.id);

      // Load user type-specific data
      if (user.type === UserType.TUTOR) {
        try {
          // Load education information for tutors
          const educationQuery = await this.dataSource.query(
            `SELECT
              id, tutor_id, degree, institution, field_of_study,
              start_date, end_date, is_current, description,
              location, grade, activities, created_at, updated_at
            FROM tutor_education
            WHERE tutor_id = $1
            ORDER BY start_date DESC`,
            [user.id]
          );

          if (educationQuery && educationQuery.length > 0) {
            user['_education'] = educationQuery.map(edu => ({
              id: edu.id,
              tutorId: edu.tutor_id,
              degree: edu.degree,
              institution: edu.institution,
              fieldOfStudy: edu.field_of_study,
              startDate: formatToYYYYMMDD(new Date(edu.start_date)) || null,
              endDate: edu.end_date ? formatToYYYYMMDD(new Date(edu.end_date)) || null : null,
              isCurrent: edu.is_current,
              description: edu.description,
              location: edu.location,
              grade: edu.grade,
              activities: edu.activities,
              createdAt: edu.created_at,
              updatedAt: edu.updated_at
            }));
          } else {
            user['_education'] = [];
          }
        } catch (error) {
          console.error(`Error loading education data for tutor ${user.id}:`, error);
          user['_education'] = [];
        }
      } else if (user.type === UserType.STUDENT) {
        try {
          // Set default skin ID from user entity (use diary skin as primary default for backward compatibility)
          user['_defaultSkinId'] = user.defaultDiarySkinId || null;

          // Load assigned tutors
          const assignedTutorsQuery = await this.studentTutorMappingRepository.find({
            where: { studentId: user.id, status: MappingStatus.ACTIVE },
            relations: ['tutor', 'planFeature']
          });

          if (assignedTutorsQuery && assignedTutorsQuery.length > 0) {
            user['_assignedTutors'] = assignedTutorsQuery.map(mapping => ({
              id: mapping.id,
              tutorId: mapping.tutorId,
              tutorName: mapping.tutor?.name || 'Unknown',
              moduleId: mapping.planFeatureId,
              moduleName: mapping.planFeature?.name || 'Unknown'
            }));
          }
        } catch (error) {
          console.error(`Error loading additional student data for user ${user.id}:`, error);
        }
      }
    }

    return new PagedListDto(
      users.map((user) => user.toDto()),
      totalCount
    );
  }

  /**
   * Find all admin users with pagination and filtering
   * @param filterDto Filter and pagination parameters
   * @returns Paginated list of admin users
   */
  async findAllAdminUsers(filterDto: any): Promise<PagedListDto<UserResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', userId, email, phoneNumber, gender } = filterDto;

    const skip = (page - 1) * limit;

    // Use query builder instead of function expressions
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .leftJoinAndSelect('user.userPlans', 'userPlans')
      .leftJoinAndSelect('userPlans.plan', 'plan')
      .where('user.isActive = :isActive', { isActive: true })
      .andWhere('user.type = :type', { type: UserType.ADMIN });

    // Add filters if provided
    if (userId) {
      // Use partial matching only if userId has at least 3 characters
      if (userId.length >= 3) {
        queryBuilder.andWhere('LOWER(user.userId) LIKE LOWER(:userId)', { userId: `%${userId}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.userId) = LOWER(:userId)', { userId });
      }
    }

    if (email) {
      // Use partial matching only if email has at least 3 characters
      if (email.length >= 3) {
        queryBuilder.andWhere('LOWER(user.email) LIKE LOWER(:email)', { email: `%${email}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.email) = LOWER(:email)', { email });
      }
    }

    if (phoneNumber) {
      // Use partial matching only if phoneNumber has at least 3 characters
      if (phoneNumber.length >= 3) {
        queryBuilder.andWhere('user.phoneNumber LIKE :phoneNumber', { phoneNumber: `%${phoneNumber}%` });
      } else {
        queryBuilder.andWhere('user.phoneNumber = :phoneNumber', { phoneNumber });
      }
    }

    if (gender) {
      // Use partial matching only if gender has at least 3 characters
      if (gender.length >= 3) {
        queryBuilder.andWhere('LOWER(user.gender) LIKE LOWER(:gender)', { gender: `%${gender}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.gender) = LOWER(:gender)', { gender });
      }
    }

    // Validate sortBy field to prevent SQL injection
    const allowedSortFields = ['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Apply sorting
    queryBuilder.orderBy(`user.${validSortBy}`, sortDirection as 'ASC' | 'DESC');

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute the query
    const [users, totalCount] = await queryBuilder.getManyAndCount();

    // Add profile picture URLs and admin-specific data
    for (const user of users) {
      user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(user.id);
    }

    return new PagedListDto(
      users.map((user) => user.toDto()),
      totalCount
    );
  }

  /**
   * Find all tutor users with pagination and filtering
   * @param filterDto Filter and pagination parameters
   * @returns Paginated list of tutor users
   */
  async findAllTutorUsers(filterDto: any): Promise<PagedListDto<UserResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', userId, email, phoneNumber, gender } = filterDto;

    const skip = (page - 1) * limit;

    // Use query builder instead of function expressions
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .leftJoinAndSelect('user.userPlans', 'userPlans')
      .leftJoinAndSelect('userPlans.plan', 'plan')
      .where('user.isActive = :isActive', { isActive: true })
      .andWhere('user.type = :type', { type: UserType.TUTOR });

    // Add filters if provided
    if (userId) {
      // Use partial matching only if userId has at least 3 characters
      if (userId.length >= 3) {
        queryBuilder.andWhere('LOWER(user.userId) LIKE LOWER(:userId)', { userId: `%${userId}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.userId) = LOWER(:userId)', { userId });
      }
    }

    if (email) {
      // Use partial matching only if email has at least 3 characters
      if (email.length >= 3) {
        queryBuilder.andWhere('LOWER(user.email) LIKE LOWER(:email)', { email: `%${email}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.email) = LOWER(:email)', { email });
      }
    }

    if (phoneNumber) {
      // Use partial matching only if phoneNumber has at least 3 characters
      if (phoneNumber.length >= 3) {
        queryBuilder.andWhere('user.phoneNumber LIKE :phoneNumber', { phoneNumber: `%${phoneNumber}%` });
      } else {
        queryBuilder.andWhere('user.phoneNumber = :phoneNumber', { phoneNumber });
      }
    }

    if (gender) {
      // Use partial matching only if gender has at least 3 characters
      if (gender.length >= 3) {
        queryBuilder.andWhere('LOWER(user.gender) LIKE LOWER(:gender)', { gender: `%${gender}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.gender) = LOWER(:gender)', { gender });
      }
    }

    // Validate sortBy field to prevent SQL injection
    const allowedSortFields = ['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Apply sorting
    queryBuilder.orderBy(`user.${validSortBy}`, sortDirection as 'ASC' | 'DESC');

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute the query
    const [users, totalCount] = await queryBuilder.getManyAndCount();

    // Add profile picture URLs and education information for tutors
    for (const user of users) {
      user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(user.id);

      // Load education information for tutors
      if (user.type === UserType.TUTOR) {
        try {
          // Load education information
          const educationQuery = await this.dataSource.query(
            `SELECT
              id, tutor_id, degree, institution, field_of_study,
              start_date, end_date, is_current, description,
              location, grade, activities, created_at, updated_at
            FROM tutor_education
            WHERE tutor_id = $1
            ORDER BY start_date DESC`,
            [user.id]
          );

          if (educationQuery && educationQuery.length > 0) {
            user['_education'] = educationQuery.map(edu => ({
              id: edu.id,
              tutorId: edu.tutor_id,
              degree: edu.degree,
              institution: edu.institution,
              fieldOfStudy: edu.field_of_study,
              startDate: formatToYYYYMMDD(new Date(edu.start_date)) || null,
              endDate: edu.end_date ? formatToYYYYMMDD(new Date(edu.end_date)) || null : null,
              isCurrent: edu.is_current,
              description: edu.description,
              location: edu.location,
              grade: edu.grade,
              activities: edu.activities,
              createdAt: edu.created_at,
              updatedAt: edu.updated_at
            }));
          } else {
            user['_education'] = [];
          }

          // Load assignment counts for tutors
          const assignedStudentCount = await this.studentTutorMappingRepository.count({
            where: { tutorId: user.id, status: MappingStatus.ACTIVE }
          });

          const assignedModuleCount = await this.studentTutorMappingRepository
            .createQueryBuilder('mapping')
            .select('COUNT(DISTINCT mapping.planFeatureId)', 'count')
            .where('mapping.tutorId = :tutorId', { tutorId: user.id })
            .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
            .getRawOne();

          user['_assignedStudentCount'] = assignedStudentCount;
          user['_assignedModuleCount'] = parseInt(assignedModuleCount?.count || '0');
        } catch (error) {
          console.error(`Error loading education data for tutor ${user.id}:`, error);
          user['_education'] = [];
          user['_assignedStudentCount'] = 0;
          user['_assignedModuleCount'] = 0;
        }
      }
    }

    return new PagedListDto(
      users.map((user) => user.toDto()),
      totalCount
    );
  }

  /**
   * Get all admin users without pagination
   * @returns Array of admin users
   */
  async getAllAdminUsers(): Promise<User[]> {
    return this.userRepository.find({
      where: {
        isActive: true,
        type: UserType.ADMIN
      },
      relations: ['userRoles', 'userRoles.role']
    });
  }

  /**
   * Find all student users with pagination and filtering
   * @param filterDto Filter and pagination parameters
   * @returns Paginated list of student users
   */
  async findAllStudentUsers(filterDto: any): Promise<PagedListDto<UserResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', userId, email, phoneNumber, gender } = filterDto;

    const skip = (page - 1) * limit;

    // Use query builder instead of function expressions
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .leftJoinAndSelect('user.userPlans', 'userPlans')
      .leftJoinAndSelect('userPlans.plan', 'plan')
      .where('user.isActive = :isActive', { isActive: true })
      .andWhere('user.type = :type', { type: UserType.STUDENT });

    // Add filters if provided
    if (userId) {
      // Use partial matching only if userId has at least 3 characters
      if (userId.length >= 3) {
        queryBuilder.andWhere('LOWER(user.userId) LIKE LOWER(:userId)', { userId: `%${userId}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.userId) = LOWER(:userId)', { userId });
      }
    }

    if (email) {
      // Use partial matching only if email has at least 3 characters
      if (email.length >= 3) {
        queryBuilder.andWhere('LOWER(user.email) LIKE LOWER(:email)', { email: `%${email}%` });
      } else {
        queryBuilder.andWhere('LOWER(user.email) = LOWER(:email)', { email });
      }
    }

    if (phoneNumber) {
      // Use partial matching only if phoneNumber has at least 3 characters
      if (phoneNumber.length >= 3) {
        queryBuilder.andWhere('user.phoneNumber LIKE :phoneNumber', { phoneNumber: `%${phoneNumber}%` });
      } else {
        queryBuilder.andWhere('user.phoneNumber = :phoneNumber', { phoneNumber });
      }
    }

    if (gender) {
      // Gender should be an exact match since it's an enumerated value
      queryBuilder.andWhere('LOWER(user.gender) = LOWER(:gender)', { gender });
    }

    // Validate sortBy field to prevent SQL injection
    const allowedSortFields = ['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Apply sorting
    queryBuilder.orderBy(`user.${validSortBy}`, sortDirection as 'ASC' | 'DESC');

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute the query
    const [users, totalCount] = await queryBuilder.getManyAndCount();

    // Add profile picture URLs and other user-specific data
    for (const user of users) {
      user['_profilePictureUrl'] = await this.profilePictureService.getProfilePictureUrl(user.id);

      // For student users, try to load additional student-specific data
      if (user.type === UserType.STUDENT) {
        try {
          // Set default skin ID from user entity (use diary skin as primary default for backward compatibility)
          user['_defaultSkinId'] = user.defaultDiarySkinId || null;

          // Load assigned tutors
          const assignedTutorsQuery = await this.studentTutorMappingRepository.find({
            where: { studentId: user.id, status: MappingStatus.ACTIVE },
            relations: ['tutor', 'planFeature']
          });

          if (assignedTutorsQuery && assignedTutorsQuery.length > 0) {
            user['_assignedTutors'] = assignedTutorsQuery.map(mapping => ({
              id: mapping.id,
              tutorId: mapping.tutorId,
              tutorName: mapping.tutor?.name || 'Unknown',
              moduleId: mapping.planFeatureId,
              moduleName: mapping.planFeature?.name || 'Unknown'
            }));
          }

          // Load assignment counts for students
          const assignedTutorCount = await this.studentTutorMappingRepository.count({
            where: { studentId: user.id, status: MappingStatus.ACTIVE }
          });

          const assignedModuleCount = await this.studentTutorMappingRepository
            .createQueryBuilder('mapping')
            .select('COUNT(DISTINCT mapping.planFeatureId)', 'count')
            .where('mapping.studentId = :studentId', { studentId: user.id })
            .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
            .getRawOne();

          user['_assignedTutorCount'] = assignedTutorCount;
          user['_assignedModuleCount'] = parseInt(assignedModuleCount?.count || '0');
        } catch (error) {
          console.error(`Error loading additional student data for user ${user.id}:`, error);
        }
      }
    }

    return new PagedListDto(
      users.map((user) => user.toDto()),
      totalCount
    );
  }

  /**
   * Get the count of students assigned to a tutor
   * @param tutorId The ID of the tutor
   * @returns The number of students assigned to the tutor
   */
  async getTutorStudentCount(tutorId: string): Promise<number> {
    // Check if tutor exists
    const tutor = await this.userRepository.findOne({
      where: {
        id: tutorId,
        type: UserType.TUTOR
      }
    });

    if (!tutor) {
      throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
    }

    // Count active students assigned to this tutor
    const activeStudentCount = await this.studentTutorMappingRepository.count({
      where: {
        tutorId: tutorId,
        status: MappingStatus.ACTIVE
      }
    });

    return activeStudentCount;
  }

  /**
   * Set default diary skin for a user
   * @param userId User ID
   * @param skinId Diary skin ID
   */
  async setDefaultDiarySkin(userId: string, skinId: string): Promise<void> {
    // Update user's default diary skin
    await this.userRepository.update(userId, {
      defaultDiarySkinId: skinId
    });

    // Also update the diary table's defaultSkinId for compatibility
    const diaryRepository = this.dataSource.getRepository(Diary);
    await diaryRepository.update({ userId }, {
      defaultSkinId: skinId
    });
  }

  /**
   * Set default novel skin for a user
   * @param userId User ID
   * @param skinId Diary skin ID (novel uses diary skins)
   */
  async setDefaultNovelSkin(userId: string, skinId: string): Promise<void> {
    await this.userRepository.update(userId, {
      defaultNovelSkinId: skinId
    });
  }

  /**
   * Get user skin preferences
   * @param userId User ID
   * @returns User skin preferences
   */
  async getUserSkinPreferences(userId: string): Promise<{ defaultDiarySkinId?: string; defaultNovelSkinId?: string }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'defaultDiarySkinId', 'defaultNovelSkinId']
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return {
      defaultDiarySkinId: user.defaultDiarySkinId,
      defaultNovelSkinId: user.defaultNovelSkinId
    };
  }

  // New improved user creation methods by admin
  async createAdminUserByAdmin(userData: CreateAdminUserByAdminDto, adminId: string): Promise<UserCreationResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    // Check if userId already exists
    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Generate secure random password
    const temporaryPassword = generateSecurePassword(12);

    // Create user with admin type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: '', // Default empty phone number
      gender: 'N/A', // Default gender
      agreedToTerms: true,
      type: UserType.ADMIN,
      isConfirmed: true, // Admin users are auto-confirmed
      isActive: true
    });

    // Set and hash the password
    newUser.setPassword(temporaryPassword);

    const savedUser = await this.userRepository.save(newUser);

    // Find admin role
    let adminRole = await this.roleRepository.findOne({ where: { name: 'admin' } });

    // Create admin role if it doesn't exist
    if (!adminRole) {
      adminRole = this.roleRepository.create({ name: 'admin' });
      adminRole = await this.roleRepository.save(adminRole);
    }

    // Assign admin role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: adminRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
    });

    await this.userRoleRepository.save(userRole);

    // Send welcome email with credentials
    try {
      await this.emailService.sendUserCreationEmail(
        userData.email,
        trimmedUserId,
        temporaryPassword,
        'Admin'
      );
    } catch (error) {
      console.error('Failed to send user creation email:', error);
      // Don't throw error here as user is already created
    }

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }

    const userDto = userWithRoles.toDto();
    return {
      ...userDto,
      temporaryPassword
    };
  }

  async createTutorUserByAdmin(userData: CreateTutorUserByAdminDto, adminId: string): Promise<UserCreationResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Generate secure random password
    const temporaryPassword = generateSecurePassword(12);

    // Create user with tutor type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: '', // Default empty phone number
      gender: 'N/A', // Default gender
      bio: '', // Default empty bio
      agreedToTerms: true,
      type: UserType.TUTOR,
      isConfirmed: true, // Auto-confirmed for admin-created tutors
      isActive: true // Auto-approved for admin-created tutors
    });

    // Set and hash the password
    newUser.setPassword(temporaryPassword);

    const savedUser = await this.userRepository.save(newUser);

    // Find tutor role
    let tutorRole = await this.roleRepository.findOne({ where: { name: 'tutor' } });

    // Create tutor role if it doesn't exist
    if (!tutorRole) {
      tutorRole = this.roleRepository.create({ name: 'tutor' });
      tutorRole = await this.roleRepository.save(tutorRole);
    }

    // Assign tutor role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: tutorRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
    });

    await this.userRoleRepository.save(userRole);

    // Create approved tutor approval record so the tutor appears in approved list
    const tutorApproval = this.tutorApprovalRepository.create({
      userId: savedUser.id,
      status: TutorApprovalStatus.APPROVED,
      adminId: adminId, // Set to the admin who created the tutor
      adminNotes: 'Auto-approved: Created by admin',
      approvedAt: getCurrentUTCDate(),
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
    });

    await this.tutorApprovalRepository.save(tutorApproval);

    // Send welcome email with credentials
    try {
      await this.emailService.sendUserCreationEmail(
        userData.email,
        trimmedUserId,
        temporaryPassword,
        'Tutor'
      );
    } catch (error) {
      console.error('Failed to send user creation email:', error);
      // Don't throw error here as user is already created
    }

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }

    const userDto = userWithRoles.toDto();
    return {
      ...userDto,
      temporaryPassword
    };
  }

  async createStudentUserByAdmin(userData: CreateStudentUserByAdminDto, adminId: string): Promise<UserCreationResponseDto> {
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictException(`User ${userData.email} already exists`);
    }

    // Trim the userId to remove any whitespace
    const trimmedUserId = trimUserId(userData.userId);

    const existingUserId = await this.findByUserId(trimmedUserId);
    if (existingUserId) {
      throw new ConflictException(`User ID ${trimmedUserId} is already taken`);
    }

    // Generate secure random password
    const temporaryPassword = generateSecurePassword(12);

    // Create user with student type
    const newUser = this.userRepository.create({
      name: trimmedUserId, // Use trimmed userId as name
      userId: trimmedUserId, // Use trimmed userId
      email: userData.email,
      phoneNumber: '', // Default empty phone number
      gender: 'N/A', // Default gender
      agreedToTerms: true,
      type: UserType.STUDENT,
      isConfirmed: true, // Auto-confirmed for admin-created students
      isActive: true
    });

    // Set and hash the password
    newUser.setPassword(temporaryPassword);

    const savedUser = await this.userRepository.save(newUser);

    // Find student role
    let studentRole = await this.roleRepository.findOne({ where: { name: 'student' } });

    // Create student role if it doesn't exist
    if (!studentRole) {
      studentRole = this.roleRepository.create({ name: 'student' });
      studentRole = await this.roleRepository.save(studentRole);
    }

    // Assign student role to user
    const userRole = this.userRoleRepository.create({
      userId: savedUser.id,
      roleId: studentRole.id,
      createdAt: getCurrentUTCDate(),
      updatedAt: getCurrentUTCDate()
    });

    await this.userRoleRepository.save(userRole);

    // Send welcome email with credentials
    try {
      await this.emailService.sendUserCreationEmail(
        userData.email,
        trimmedUserId,
        temporaryPassword,
        'Student'
      );
    } catch (error) {
      console.error('Failed to send user creation email:', error);
      // Don't throw error here as user is already created
    }

    // Refresh user with roles
    const userWithRoles = await this.findById(savedUser.id);
    if (!userWithRoles) {
      throw new NotFoundException('User with roles not found');
    }

    const userDto = userWithRoles.toDto();
    return {
      ...userDto,
      temporaryPassword
    };
  }
}
