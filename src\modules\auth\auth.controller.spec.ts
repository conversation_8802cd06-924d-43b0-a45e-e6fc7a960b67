import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            login: jest.fn(),
            register: jest.fn(),
            forgotPassword: jest.fn(),
            resetPassword: jest.fn(),
            verifyEmail: jest.fn(),
            resendVerificationEmail: jest.fn(),
            changePassword: jest.fn(),
            createRole: jest.fn(),
            forgotUserId: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  let authService: any;

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const loginDto = {
        userId: 'TEST001',
        password: 'password123'
      };

      const mockLoginResponse = {
        access_token: 'jwt-token',
        user: {
          id: 'user-1',
          userId: 'TEST001',
          email: '<EMAIL>',
          type: 'student'
        }
      };

      authService.login.mockResolvedValue(mockLoginResponse);

      const result = await controller.login(loginDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLoginResponse);
      expect(result.message).toBe('Login successful');
      expect(authService.login).toHaveBeenCalled();
    });
  });

  describe('register', () => {
    it('should register student user successfully', async () => {
      const registerDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '**********',
        gender: 'male',
        type: 'student',
        agreedToTerms: true,
        toCreateUserDto: jest.fn()
      } as any;

      const mockUser = {
        id: 'user-1',
        userId: 'STUDENT001',
        email: '<EMAIL>',
        type: 'student'
      };

      authService.register.mockResolvedValue(mockUser);

      const result = await controller.register(registerDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);
      expect(result.message).toBe('Registration successful. Please check your email to verify your account.');
      expect(authService.register).toHaveBeenCalledWith(registerDto);
    });

    it('should register tutor user successfully', async () => {
      const registerDto = {
        userId: 'TUTOR001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '**********',
        gender: 'male',
        type: 'tutor',
        agreedToTerms: true,
        toCreateUserDto: jest.fn()
      } as any;

      const mockUser = {
        id: 'user-1',
        userId: 'TUTOR001',
        email: '<EMAIL>',
        type: 'tutor'
      };

      authService.register.mockResolvedValue(mockUser);

      const result = await controller.register(registerDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);
      expect(result.message).toBe('Registration successful. Please check your email to verify your account.');
      expect(authService.register).toHaveBeenCalledWith(registerDto);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const verifyEmailDto = {
        token: 'verification-token'
      };

      const mockResponse = {
        message: 'Email verified successfully'
      };

      authService.verifyEmail.mockResolvedValue(mockResponse);

      const result = await controller.verifyEmail(verifyEmailDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Email verified successfully');
      expect(authService.verifyEmail).toHaveBeenCalledWith('verification-token');
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email successfully', async () => {
      const forgotPasswordDto = {
        identifier: '<EMAIL>'
      } as any;

      const mockResponse = {
        message: 'Password reset email sent'
      };

      authService.forgotPassword.mockResolvedValue(mockResponse);

      const result = await controller.forgotPassword(forgotPasswordDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Password reset email sent successfully');
      expect(authService.forgotPassword).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const resetPasswordDto = {
        token: 'reset-token',
        newPassword: 'newpassword123'
      };

      const mockResponse = {
        message: 'Password reset successfully'
      };

      authService.resetPassword.mockResolvedValue(mockResponse);

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Password reset successfully');
      expect(authService.resetPassword).toHaveBeenCalledWith('reset-token', 'newpassword123');
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const changePasswordDto = {
        userId: 'user-1',
        currentPassword: 'oldpassword123',
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword123'
      } as any;

      const mockReq = {
        user: { sub: 'user-1' }
      } as any;

      const mockResponse = {
        access_token: 'new-jwt-token',
        user: {
          id: 'user-1',
          userId: 'TEST001',
          email: '<EMAIL>'
        }
      };

      authService.changePassword.mockResolvedValue(mockResponse);

      const result = await controller.changePassword(changePasswordDto, mockReq);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(result.message).toBe('Password changed successfully. Please use your new password for future logins.');
      expect(authService.changePassword).toHaveBeenCalledWith(changePasswordDto, mockReq);
    });
  });
});
