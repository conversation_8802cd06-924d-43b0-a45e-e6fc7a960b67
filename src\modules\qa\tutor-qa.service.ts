import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { QAQuestion } from '../../database/entities/qa-question.entity';
import { QAAssignment } from '../../database/entities/qa-assignment.entity';
import { QASubmission } from '../../database/entities/qa-submission.entity';
import { QASubscription } from '../../database/entities/qa-subscription.entity';
import { QAAssignmentStatus } from '../../database/entities/qa-assignment.entity';
import { QASubmissionStatus } from '../../database/entities/qa-submission.entity';
import { 
  CreateQAQuestionDto, 
  CreateQAAssignmentDto, 
  CreateQASubmissionDto,
  QAQuestionResponseDto,
  QAAssignmentResponseDto,
  QASubmissionResponseDto,
  ReviewSubmissionDto,
  UpdateQAQuestionDto,
  QAQuestionPaginationDto,
  QAAssignmentPaginationDto,
  CreateQAAssignmentItemsDto,
  QAAssignmentItemsResponseDto,
  SubmissionHistoryDto,
  AssignmentSubmissionDto,
  AssignmentItemDto,
  SubmissionDetailResponseDto,
  AssignmentSetDto
} from '../../database/models/qa.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationService } from '../../common/services/pagination.service';
import { User, UserType } from 'src/database/entities/user.entity';
import { QAAssignmentSets } from 'src/database/entities/qa-assignment-sets.entity';
import { QAAssignmentItems } from 'src/database/entities/qa-assignment-items.entity';

@Injectable()
export class TutorQAService {
  private readonly logger = new Logger(TutorQAService.name);

  constructor(
    private readonly paginationService: PaginationService,
    @InjectRepository(QAQuestion)
    private readonly qaQuestionRepository: Repository<QAQuestion>,
    @InjectRepository(QAAssignment)
    private qaAssignmentRepository: Repository<QAAssignment>,
    @InjectRepository(QAAssignmentItems)
    private qaAssignmentItemsRepository: Repository<QAAssignmentItems>,
    @InjectRepository(QAAssignmentSets)
    private qaAssignmentSetRepository: Repository<QAAssignmentSets>,
    @InjectRepository(QASubmission)
    private qaSubmissionRepository: Repository<QASubmission>,
    @InjectRepository(QASubscription)
    private qaSubscriptionRepository: Repository<QASubscription>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource
  ) {}

  async getStudentDropdownList(searchQuery?: string): Promise<any[]> {
    try {
      // Create query builder
      const queryBuilder = this.userRepository
        .createQueryBuilder('user')
        .select([
          'user.id as id',
          'user.name as name',
          'user.userId as userId',
          'user.email as email'
        ])
        .where('user.isActive = :isActive', { isActive: true })
        .andWhere('user.type = :type', { type: UserType.STUDENT });

      // Add search filter if provided
      if (searchQuery && searchQuery.trim().length > 0) {
        // Use partial matching if search query has at least 3 characters
        if (searchQuery.length >= 3) {
          queryBuilder.andWhere(
            '(LOWER(user.name) LIKE LOWER(:search) OR ' +
            'LOWER(user.userId) LIKE LOWER(:search) OR ' +
            'LOWER(user.email) LIKE LOWER(:search))',
            { search: `%${searchQuery}%` }
          );
        } else {
          // Use exact matching for short queries
          queryBuilder.andWhere(
            '(LOWER(user.name) = LOWER(:search) OR ' +
            'LOWER(user.userId) = LOWER(:search) OR ' +
            'LOWER(user.email) = LOWER(:search))',
            { search: searchQuery }
          );
        }
      }

      // Order by name for better usability
      queryBuilder.orderBy('user.name', 'ASC');

      // Limit results for performance
      queryBuilder.limit(50);

      // Execute raw query to get the exact fields we need
      const students = await queryBuilder.getRawMany();

      return students;
    } catch (error) {
      this.logger.error(`Failed to fetch student dropdown list: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch student dropdown list');
    }
  }

  async getPendingSubmissions(
    paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: { status: QASubmissionStatus.SUBMITTED },
      skip,
      take,
      relations: ['assignment-set', 'assignment.question']
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map(s => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto
    );

    return ApiResponse.success(
      pagedList,
      'Pending submissions retrieved successfully'
    );
  }

  async getReviewedSubmissions(
    tutorId: string, 
    paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);
    
    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: { 
        reviewedBy: tutorId,
        status: QASubmissionStatus.REVIEWED 
      },
      skip,
      take,
      relations: ['assignmentSet', 'assignment.question']
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map(s => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto
    );

    return ApiResponse.success(
      pagedList,
      'Reviewed submissions retrieved successfully'
    );
  }

  private transformToSubmissionResponse(submission: QASubmission): QASubmissionResponseDto {
    return {
      id: submission.id,
      score: submission.points,
      answer: submission.answer,
      status: submission.status,
      submissionDate: submission.submissionDate,
      feedback: submission.feedback,
      corrections: submission.corrections,
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt
    };
  }

  async createAssignments(dto: CreateQAAssignmentItemsDto): Promise<QAAssignmentItemsResponseDto[]> {

    const lastSet = await this.qaAssignmentSetRepository.findOne({
      where: { },
      order: { setSequence: 'DESC' }
    });
    const nextSequence = lastSet ? lastSet.setSequence + 1 : 1;

    const questions = await this.qaQuestionRepository.findBy({
      id: In(dto.questionIds),
    });

    // Step 2: Sum the mark values
    const totalMark = questions.reduce((sum, q) => sum + (q.points || 0), 0);

    const assignmentSet = this.qaAssignmentSetRepository.create({
      setSequence: nextSequence,
      instructions: dto.instructions,
      score: totalMark
    });
    await this.qaAssignmentSetRepository.save(assignmentSet);

    const assignments = this.qaAssignmentItemsRepository.create(
      dto.questionIds.map(qid => ({
        questionId: qid,
        studentId: dto.studentId,
        setSequence: nextSequence,
        assignedDate: new Date(),
        status: QAAssignmentStatus.ASSIGNED
      }))
    );

    const savedAssignments = await this.qaAssignmentItemsRepository.save(assignments);

    return savedAssignments.map(a => ({
      id: a.id,
      questionIds: [a.questionId],
      studentId: a.studentId,
      points: a.score,
      deadline: a.deadline,
      status: a.status,
      assignedDate: a.assignedDate,
      createdAt: a.createdAt,
      updatedAt: a.updatedAt,
    }));
  }

  async getSubmissionById(id: string): Promise<any> { // <-- Changed return type from Promise<QATaskSubmissionHistory> to Promise<any>
    const submission = await this.qaSubmissionRepository.findOne({
      where: { id },
      relations: ['assignmentSet']
    });

    if (!submission) {
      throw new NotFoundException(`Submission history with id ${id} not found.`);
    }

    const assignmentItems = await this.qaAssignmentItemsRepository.find({
      where: { setSequence: submission.setSequence },
      relations: ['question'],
    });

  const assignmentSubmission: AssignmentSubmissionDto = {
    id: submission.id,
    answer: submission.answer,
    status: submission.status,
    score: submission.points,
    feedback: submission.feedback,
    corrections: submission.corrections,    
    wordCount: submission.answer ? submission.answer.split(' ').length : 0,
    submissionDate: submission.submissionDate,
    sequenceNumber: submission.setSequence,
  };
    const assignmentSet: AssignmentSetDto = submission.assignmentSet ? {
      id: submission.assignmentSet.id,
      setSequence: submission.assignmentSet.setSequence,
      instruction: submission.assignmentSet.instructions,
      score: submission.assignmentSet.score
    } : null;

  const question: AssignmentItemDto[] = assignmentItems.map(item => ({
    id: item.id,
    setSequence: item.setSequence,
    status: item.status,
    assignedDate: item.assignedDate,
    question: item.question ? [{
      id: item.question.id,
      question: item.question.question,
    }] : [],
  }));

  const response: SubmissionDetailResponseDto = {
    assignmentSubmission,
    assignmentSet,
    question,
  };

  return response;
  }

  //   async getSubmissionById(id: string): Promise<any> { // <-- Changed return type from Promise<QATaskSubmissionHistory> to Promise<any>
  //   const submission = await this.qaSubmissionRepository.findOne({
  //     where: { id },
  //     relations: ['assignmentSet']
  //   });

  //   if (!submission) {
  //     throw new NotFoundException(`Submission history with id ${id} not found.`);
  //   }

  //   const assignmentItems = await this.qaAssignmentItemsRepository.find({
  //     where: { setSequence: submission.setSequence },
  //     relations: ['question', 'assignmentSet'],
  //   });

  // const assignmentSubmission: AssignmentSubmissionDto = {
  //   id: submission.id,
  //   answer: submission.answer,
  //   status: submission.status,
  //   feedback: submission.feedback,
  //   corrections: submission.corrections,
  //   wordCount: submission.answer ? submission.answer.split(' ').length : 0,
  //   submissionDate: submission.submissionDate,
  //   sequenceNumber: submission.setSequence,
  // };

  // const question: AssignmentItemDto[] = assignmentItems.map(item => ({
  //   id: item.id,
  //   setSequence: assignmentItems.setSequence,
  //   status: item.status,
  //   assignedDate: item.assignedDate,
  //   question: AssignmentQDto = item.question ? [{
  //     id: item.question.id,
  //     question: item.question.question
  //   }] : [],
  //   assignmentSet: submission.assignmentSet ? {
  //     id: submission.assignmentSet.id,
  //     setSequence: submission.assignmentSet.setSequence,
  //     instruction: submission.assignmentSet.instructions
  //   } : null
  // }));

  // // return {
  // //   submissionHistory,
  // //   question
  // // };

  // return submission;


  //   // return {
  //   //   submissionHistory: {
  //   //     id: submission.id,
  //   //     content: submission.answer,
  //   //     status: submission.status,
  //   //     feedback: submission.feedback,
  //   //     corrections: submission.corrections,
  //   //     wordCount: submission.answer ? submission.answer.split(' ').length : 0,
  //   //     submissionDate: submission.submissionDate,
  //   //     sequenceNumber: submission.setSequence,
  //   //   },
  //   //   question: assignmentItems ? assignmentItems: null
  //   // };
  // }
}