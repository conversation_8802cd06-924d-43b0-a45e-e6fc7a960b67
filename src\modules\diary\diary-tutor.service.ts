import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { User } from '../../database/entities/user.entity';
import { TutorProfileDto, StudentTutorListResponseDto } from '../../database/models/diary.dto';
import { PlanFeature } from '../../database/entities/plan-feature.entity';

@Injectable()
export class DiaryTutorService {
  private readonly logger = new Logger(DiaryTutorService.name);

  constructor(
    @InjectRepository(Diary)
    private readonly diaryRepository: Repository<Diary>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService,
  ) {}

  /**
   * Update the tutor greeting message for a student's diary
   * @param userId Student user ID
   * @param greeting Greeting message for the tutor
   * @returns Updated diary
   */
  async updateTutorGreeting(userId: string, greeting: string): Promise<Diary> {
    try {
      // Get the diary
      const diary = await this.diaryRepository.findOne({
        where: { userId: userId },
        relations: ['defaultSkin'],
      });

      if (!diary) {
        throw new Error(`Diary not found for user ${userId}`);
      }

      // Update the greeting
      diary.tutorGreeting = greeting;

      // Save the updated diary
      const updatedDiary = await this.diaryRepository.save(diary);

      // Add the hasGreeting property
      (updatedDiary as any).hasGreeting = true;

      // Get the student information for the notification
      const student = await this.userRepository.findOne({ where: { id: userId } });
      if (!student) {
        this.logger.warn(`Student with ID ${userId} not found when sending greeting notification`);
        return updatedDiary;
      }

      // Get the diary module feature ID
      const diaryModuleId = await this.getDiaryModuleFeatureId();
      if (!diaryModuleId) {
        this.logger.warn('Could not find diary module feature ID for notification');
        return updatedDiary;
      }

      // Get the assigned tutor for the diary module
      const studentTutorMapping = await this.tutorMatchingService.getStudentTutorForModule(
        userId,
        diaryModuleId
      );

      if (studentTutorMapping && studentTutorMapping.tutorId) {
        try {
          // Generate a deep link to the student's profile
          const profileLink = this.deeplinkService.getWebLink(DeeplinkType.PROFILE, {
            userId: userId,
            userType: 'student'
          });

          // Create HTML content for email notification
          const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Student Greeting</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello,</p>
              <p>${student.name} has set a greeting message for you:</p>
              <div style="background-color: #f9f9f9; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0;">
                <p style="font-style: italic;">"${greeting}"</p>
              </div>
              <p>You can view their profile and start interacting with them.</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${profileLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Student Profile</a>
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
          `;

          // Send notification to the tutor
          await this.notificationHelper.notify(
            studentTutorMapping.tutorId,
            NotificationType.TUTOR_GREETING,
            'New Student Greeting',
            `${student.name} has set a greeting message for you.`,
            {
              relatedEntityId: diary.id,
              relatedEntityType: 'diary',
              htmlContent: htmlContent,
              // Use all notification channels
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );

          this.logger.log(`Sent greeting notification to tutor ${studentTutorMapping.tutorId} from student ${userId}`);
        } catch (notificationError) {
          this.logger.error(`Failed to send greeting notification: ${notificationError.message}`, notificationError.stack);
          // Continue execution even if notification fails
        }
      } else {
        this.logger.log(`No tutor assigned for student ${userId} and diary module ${diaryModuleId}`);
      }

      return updatedDiary;
    } catch (error) {
      this.logger.error(`Error updating tutor greeting for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the diary module feature ID
   */
  private async getDiaryModuleFeatureId(): Promise<string | null> {
    try {
      // Import the PlanFeature entity dynamically to avoid circular dependencies
      const { PlanFeature, FeatureType } = require('../../database/entities/plan-feature.entity');

      // Find the diary module feature
      const diaryFeature = await this.planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_USER_DIARY }
      });

      if (!diaryFeature) {
        this.logger.warn('Diary module feature not found');
        return null;
      }

      return diaryFeature.id;
    } catch (error) {
      this.logger.error(`Error getting diary module feature ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get a list of tutors who have reviewed a student's diary entries
   * @param userId Student user ID
   * @returns List of tutors
   */
  async getStudentTutors(userId: string): Promise<StudentTutorListResponseDto> {
    try {
      // Find the diary
      const diary = await this.diaryRepository.findOne({
        where: { userId: userId }
      });

      if (!diary) {
        throw new Error(`Diary not found for user ${userId}`);
      }

      // Find all entries for this diary that have been confirmed
      const entries = await this.diaryEntryRepository.find({
        where: { diaryId: diary.id, status: DiaryEntryStatus.CONFIRM },
        relations: ['feedbacks', 'feedbacks.tutor'],
      });

      // Extract unique tutors
      const tutorMap = new Map<string, TutorProfileDto>();

      for (const entry of entries) {
        for (const feedback of entry.feedbacks) {
          if (!tutorMap.has(feedback.tutorId)) {
            tutorMap.set(feedback.tutorId, {
              id: feedback.tutorId,
              name: feedback.tutor?.name,
              profilePicture: feedback.tutor?.profilePicture,
              bio: feedback.tutor?.bio,
            });
          }
        }
      }

      return {
        tutors: Array.from(tutorMap.values()),
      };
    } catch (error) {
      this.logger.error(`Error getting student tutors for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
