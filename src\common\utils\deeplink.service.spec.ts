import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { DeeplinkService, DeeplinkType } from './deeplink.service';
import { UserType } from '../../database/entities/user.entity';
import { NotificationType } from '../../database/entities/notification.entity';

describe('DeeplinkService', () => {
  let service: DeeplinkService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      if (key === 'FRONTEND_URL') return 'http://test-frontend.com';
      if (key === 'MOBILE_APP_URL') return 'testapp://';
      return null;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeeplinkService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<DeeplinkService>(DeeplinkService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getWebLink', () => {
    it('should generate profile web links correctly', () => {
      const userId = 'user123';

      expect(service.getWebLink(DeeplinkType.PROFILE, { userId, userType: UserType.ADMIN }))
        .toBe('http://test-frontend.com/profiles/admin/user123');

      expect(service.getWebLink(DeeplinkType.PROFILE, { userId, userType: UserType.TUTOR }))
        .toBe('http://test-frontend.com/tutors/profile/user123');

      expect(service.getWebLink(DeeplinkType.PROFILE, { userId, userType: UserType.STUDENT }))
        .toBe('http://test-frontend.com/dashboard/students/profile/user123');
    });

    it('should generate diary entry web links correctly', () => {
      expect(service.getWebLink(DeeplinkType.DIARY_ENTRY, { id: 'entry123' }))
        .toBe('http://test-frontend.com/diary/entries/entry123');
    });

    it('should generate verification web links correctly', () => {
      expect(service.getWebLink(DeeplinkType.VERIFICATION, { additionalParams: { token: 'abc123' } }))
        .toBe('http://test-frontend.com/verify-email?token=abc123');
    });

    it('should generate notification web links correctly', () => {
      expect(service.getWebLink(DeeplinkType.NOTIFICATION, {
        id: 'notif123',
        notificationType: NotificationType.DIARY_FEEDBACK,
      })).toBe('http://test-frontend.com/diary?entryId=notif123');
    });
  });

  describe('getDeepLink', () => {
    it('should generate profile deep links correctly', () => {
      const userId = 'user123';

      expect(service.getDeepLink(DeeplinkType.PROFILE, { userId, userType: UserType.ADMIN }))
        .toBe('testapp://profiles/admin/user123');

      expect(service.getDeepLink(DeeplinkType.PROFILE, { userId, userType: UserType.TUTOR }))
        .toBe('testapp://tutors/profile/user123');

      expect(service.getDeepLink(DeeplinkType.PROFILE, { userId, userType: UserType.STUDENT }))
        .toBe('testapp://dashboard/students/profile/user123');
    });

    it('should generate diary entry deep links correctly', () => {
      expect(service.getDeepLink(DeeplinkType.DIARY_ENTRY, { id: 'entry123' }))
        .toBe('testapp://diary/entries/entry123');
    });
  });

  describe('getLinkHtml', () => {
    it('should generate HTML links correctly', () => {
      const link = service.getLinkHtml(DeeplinkType.PROFILE, {
        userId: 'user123',
        userType: UserType.TUTOR,
        linkText: 'View Tutor',
        cssClass: 'tutor-link'
      });

      expect(link).toBe('<a href="http://test-frontend.com/tutors/profile/user123" class="tutor-link">View Tutor</a>');
    });

    it('should generate button-styled HTML links correctly', () => {
      const button = service.getLinkHtml(DeeplinkType.PROFILE, {
        userId: 'user123',
        userType: UserType.TUTOR,
        linkText: 'View Tutor',
        buttonStyle: true
      });

      expect(button).toContain('background-color: #4CAF50');
      expect(button).toContain('View Tutor');
      expect(button).toContain('http://test-frontend.com/tutors/profile/user123');
    });
  });

  describe('backward compatibility', () => {
    it('should support legacy profile link methods', () => {
      const userId = 'user123';
      const userType = UserType.TUTOR;

      expect(service.getProfileLink(userId, userType))
        .toBe('http://test-frontend.com/tutors/profile/user123');

      expect(service.getProfileDeepLink(userId, userType))
        .toBe('testapp://tutors/profile/user123');

      expect(service.getProfileLinkHtml(userId, userType, 'View Tutor', 'tutor-link'))
        .toBe('<a href="http://test-frontend.com/tutors/profile/user123" class="tutor-link">View Tutor</a>');

      expect(service.getProfileButtonHtml(userId, userType, 'View Tutor'))
        .toContain('background-color: #4CAF50');
    });

    it('should generate student profile links with new routes', () => {
      const studentId = 'student123';
      const userType = UserType.STUDENT;

      expect(service.getProfileLink(studentId, userType))
        .toBe('http://test-frontend.com/dashboard/students/profile/student123');

      expect(service.getProfileDeepLink(studentId, userType))
        .toBe('testapp://dashboard/students/profile/student123');
    });
  });
});
