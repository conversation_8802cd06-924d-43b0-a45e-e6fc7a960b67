import { 
  Controller, 
  Get, 
  Put, 
  Param, 
  Body, 
  Query,
  UseGuards,
  Request, 
  Post,
  ParseUUIDPipe,
  NotFoundException
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from "@nestjs/swagger";
import { JwtAuthGuard } from "src/common/guards/jwt.guard";
import { TutorGuard } from "src/common/guards/tutor.guard";
import { ApiOkResponseWithType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { ApiResponse } from "src/common/dto/api-response.dto";
import { TutorQAMissionService } from "./tutor-qa-mission.service";
import { QATaskSubmissionMarkingDto, CreateQATaskSubmissionMarkingDto, QATaskDto } from 'src/database/models/qa-mission.dto';
import { PagedListDto } from "src/common/models/paged-list.dto";
import { QASubmissionWithDetailsDto } from "src/database/models/qa.dto";

@ApiTags('Tutor Q&A Mission')
@ApiBearerAuth('JWT-auth')
@Controller('tutor-qa-mission')
export class TutorQAMissionController {
  constructor(
    private readonly tutorQAMissionService: TutorQAMissionService
  ){}

  @Post('QAMarking')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Mark QA submission' })
  @ApiBody({
    type: CreateQATaskSubmissionMarkingDto,
    description: 'QA task submission marking data',
    examples: {
      'example1': {
        value: {
          submissionId: '123-456-789-abc-def-ghi',
          score: 85,
          submissionFeedback: 'Great job!',
          taskRemarks: 'Well done on the answers provided.',
        },
      },
  }})
  @ApiOkResponseWithType(QATaskSubmissionMarkingDto)
  @ApiErrorResponse(400, 'Bad Request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Not Found')
  async markQATaskSubmission(
    @Body() qaSubmissionMarkDto: CreateQATaskSubmissionMarkingDto
  ): Promise<ApiResponse<QATaskSubmissionMarkingDto>> {
    const result = await this.tutorQAMissionService.markQATaskSubmission(qaSubmissionMarkDto);
    return ApiResponse.success(result, "QA task submission marked successfully");
  }

  @Get('QASubmissions')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get all pending QA submissions for tutor’s students' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'timeFrequency',
    required: false,
    enum: ['weekly', 'monthly'],
    description: 'Filter by time frequency',
  })
  @ApiOkResponseWithType(PagedListDto<QASubmissionWithDetailsDto>)
  @ApiErrorResponse(400, 'Bad Request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Not Found')
  async getStudentSubmissionsForTutor(    
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('timeFrequency') timeFrequency?: 'weekly' | 'monthly'
  ): Promise<ApiResponse<PagedListDto<QASubmissionWithDetailsDto>>> {
    const result = await this.tutorQAMissionService.getStudentSubmissionsForTutor(req.user.id, { page, limit }, timeFrequency ? { timeFrequency } : undefined);
    return result;
  }

@Get('submission-history/:id')
async getSubmissionHistoryById(@Param('id') id: string) {
  const result = await this.tutorQAMissionService.getSubmissionHistoryById(id);
  if (!result) {
    throw new NotFoundException('Submission history not found');
  }
  return result;
}

  // @Get('task-submission/:id')
  //   @ApiOperation({ summary: 'Get a specific QA task by ID   ' })
  //   @ApiParam({ name: 'id', description: 'ID of the QA task to retrieve', type: String })
  //   @ApiOkResponseWithType(QATaskDto, 'QA task retrieved successfully')
  //   @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  //   @ApiErrorResponse(403, 'Forbidden - Admin access required')
  //   @ApiErrorResponse(404, 'QA task not found')
  //   async findTask(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
  //     const result = await this.tutorQAMissionService.findTaskSubmissionById(id);
  //     return ApiResponse.success(result, 'QA task retrieved successfully');
  //   }

}