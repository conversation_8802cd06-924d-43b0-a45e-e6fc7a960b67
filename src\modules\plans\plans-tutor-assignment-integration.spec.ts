import { Test, TestingModule } from '@nestjs/testing';
import { PlansService } from './plans.service';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Plan } from '../../database/entities/plan.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { Logger } from '@nestjs/common';

describe('PlansService - Tutor Assignment Integration', () => {
  let plansService: PlansService;
  let tutorMatchingService: TutorMatchingService;
  let planRepository: Repository<Plan>;
  let planFeatureRepository: Repository<PlanFeature>;
  let userRepository: Repository<User>;

  // Mock data
  const mockStudent = {
    id: 'student-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    type: UserType.STUDENT,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor1 = {
    id: 'tutor-1',
    name: 'Jane Smith',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor2 = {
    id: 'tutor-2',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockFeature1 = {
    id: 'feature-1',
    name: 'English Writing',
    type: 'MODULE',
    description: 'Writing module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockFeature2 = {
    id: 'feature-2',
    name: 'English Speaking',
    type: 'MODULE',
    description: 'Speaking module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockFeature3 = {
    id: 'feature-3',
    name: 'English Reading',
    type: 'MODULE',
    description: 'Reading module',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockPlan = {
    id: 'plan-1',
    name: 'Premium English Plan',
    features: [mockFeature1, mockFeature2, mockFeature3],
    isActive: true
  } as Plan;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlansService,
        {
          provide: TutorMatchingService,
          useValue: {
            getStudentTutorAssignments: jest.fn(),
            getOrSelectPreferredTutor: jest.fn(),
            getStudentTutorForModule: jest.fn(),
            autoAssignTutorsWithoutNotifications: jest.fn(),
            autoAssignTutorsWithPreference: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Plan),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        // Add other required dependencies as mocks
        {
          provide: 'UserSubscriptionRepository',
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: 'PaymentService',
          useValue: {
            processPayment: jest.fn(),
          },
        },
        {
          provide: 'NotificationHelper',
          useValue: {
            notify: jest.fn(),
          },
        },
      ],
    }).compile();

    plansService = module.get<PlansService>(PlansService);
    tutorMatchingService = module.get<TutorMatchingService>(TutorMatchingService);
    planRepository = module.get<Repository<Plan>>(getRepositoryToken(Plan));
    planFeatureRepository = module.get<Repository<PlanFeature>>(getRepositoryToken(PlanFeature));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));

    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  describe('assignTutorsForPlan', () => {
    it('should assign same tutor to all features for new student', async () => {
      const studentId = mockStudent.id;
      const featuresToAssign = [mockFeature1, mockFeature2, mockFeature3];

      // Mock no existing assignments for student
      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([]);
      
      // Mock preferred tutor selection
      jest.spyOn(tutorMatchingService, 'getOrSelectPreferredTutor').mockResolvedValue(mockTutor1);

      // Mock no existing assignments for each feature
      jest.spyOn(tutorMatchingService, 'getStudentTutorForModule').mockResolvedValue(null);

      // Mock successful assignments for all features
      const mockAssignments = featuresToAssign.map((feature, index) => ({
        id: `assignment-${index + 1}`,
        studentId,
        tutorId: mockTutor1.id, // Same tutor for all
        planFeatureId: feature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        tutor: mockTutor1,
        planFeature: feature,
        student: mockStudent
      }));

      jest.spyOn(tutorMatchingService, 'autoAssignTutorsWithoutNotifications')
        .mockResolvedValueOnce([mockAssignments[0]])
        .mockResolvedValueOnce([mockAssignments[1]])
        .mockResolvedValueOnce([mockAssignments[2]]);

      const result = await plansService.assignTutorsForPlan(studentId, featuresToAssign);

      // Verify all assignments use the same tutor
      expect(result).toHaveLength(3);
      expect(result.every(assignment => assignment.tutorId === mockTutor1.id)).toBe(true);
      
      // Verify preferred tutor was selected once
      expect(tutorMatchingService.getOrSelectPreferredTutor).toHaveBeenCalledWith(studentId);
      
      // Verify assignments were made for all features
      expect(tutorMatchingService.autoAssignTutorsWithoutNotifications).toHaveBeenCalledTimes(3);
    });

    it('should use existing tutor for new features when student already has assignments', async () => {
      const studentId = mockStudent.id;
      const featuresToAssign = [mockFeature2, mockFeature3]; // Adding new features

      // Mock existing assignment with tutor1
      const existingAssignment = {
        id: 'existing-assignment',
        studentId,
        tutorId: mockTutor1.id,
        planFeatureId: mockFeature1.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date('2024-01-01'),
        tutor: mockTutor1,
        planFeature: mockFeature1
      } as StudentTutorMapping;

      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([existingAssignment]);
      
      // Mock preferred tutor returns existing tutor
      jest.spyOn(tutorMatchingService, 'getOrSelectPreferredTutor').mockResolvedValue(mockTutor1);

      // Mock no existing assignments for new features
      jest.spyOn(tutorMatchingService, 'getStudentTutorForModule').mockResolvedValue(null);

      // Mock successful assignments for new features with same tutor
      const mockNewAssignments = featuresToAssign.map((feature, index) => ({
        id: `new-assignment-${index + 1}`,
        studentId,
        tutorId: mockTutor1.id, // Same tutor as existing assignment
        planFeatureId: feature.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        tutor: mockTutor1,
        planFeature: feature,
        student: mockStudent
      }));

      jest.spyOn(tutorMatchingService, 'autoAssignTutorsWithoutNotifications')
        .mockResolvedValueOnce([mockNewAssignments[0]])
        .mockResolvedValueOnce([mockNewAssignments[1]]);

      const result = await plansService.assignTutorsForPlan(studentId, featuresToAssign);

      // Verify all new assignments use the same tutor as existing assignment
      expect(result).toHaveLength(2);
      expect(result.every(assignment => assignment.tutorId === mockTutor1.id)).toBe(true);
      
      // Verify existing tutor was used (not a new selection)
      expect(tutorMatchingService.getOrSelectPreferredTutor).toHaveBeenCalledWith(studentId);
    });

    it('should handle assignment failures gracefully', async () => {
      const studentId = mockStudent.id;
      const featuresToAssign = [mockFeature1, mockFeature2];

      // Mock no existing assignments
      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([]);
      jest.spyOn(tutorMatchingService, 'getOrSelectPreferredTutor').mockResolvedValue(mockTutor1);
      jest.spyOn(tutorMatchingService, 'getStudentTutorForModule').mockResolvedValue(null);

      // Mock successful assignment for first feature, failure for second
      const successfulAssignment = {
        id: 'assignment-1',
        studentId,
        tutorId: mockTutor1.id,
        planFeatureId: mockFeature1.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        tutor: mockTutor1,
        planFeature: mockFeature1,
        student: mockStudent
      };

      jest.spyOn(tutorMatchingService, 'autoAssignTutorsWithoutNotifications')
        .mockResolvedValueOnce([successfulAssignment])
        .mockRejectedValueOnce(new Error('Assignment failed'));

      const result = await plansService.assignTutorsForPlan(studentId, featuresToAssign);

      // Should return only successful assignments
      expect(result).toHaveLength(1);
      expect(result[0].tutorId).toBe(mockTutor1.id);
      
      // Should have attempted both assignments
      expect(tutorMatchingService.autoAssignTutorsWithoutNotifications).toHaveBeenCalledTimes(2);
    });
  });

  describe('fixMissingTutorAssignments', () => {
    it('should use consistent tutor assignment for missing assignments', async () => {
      const studentId = mockStudent.id;
      const planFeatures = [mockFeature1, mockFeature2, mockFeature3];

      // Mock existing assignment for feature1 only
      const existingAssignment = {
        id: 'existing-assignment',
        studentId,
        tutorId: mockTutor1.id,
        planFeatureId: mockFeature1.id,
        status: MappingStatus.ACTIVE,
        assignedDate: new Date(),
        tutor: mockTutor1,
        planFeature: mockFeature1
      } as StudentTutorMapping;

      jest.spyOn(tutorMatchingService, 'getStudentTutorAssignments').mockResolvedValue([existingAssignment]);

      // Mock missing assignments for features 2 and 3
      jest.spyOn(tutorMatchingService, 'getStudentTutorForModule')
        .mockResolvedValueOnce(existingAssignment) // feature1 - has assignment
        .mockResolvedValueOnce(null) // feature2 - missing
        .mockResolvedValueOnce(null); // feature3 - missing

      // Mock successful assignments for missing features with same tutor
      const mockNewAssignments = [
        {
          id: 'new-assignment-2',
          studentId,
          tutorId: mockTutor1.id, // Same tutor
          planFeatureId: mockFeature2.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          tutor: mockTutor1,
          planFeature: mockFeature2,
          student: mockStudent
        },
        {
          id: 'new-assignment-3',
          studentId,
          tutorId: mockTutor1.id, // Same tutor
          planFeatureId: mockFeature3.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          tutor: mockTutor1,
          planFeature: mockFeature3,
          student: mockStudent
        }
      ];

      jest.spyOn(tutorMatchingService, 'autoAssignTutorsWithoutNotifications')
        .mockResolvedValueOnce([mockNewAssignments[0]])
        .mockResolvedValueOnce([mockNewAssignments[1]]);

      const result = await plansService.fixMissingTutorAssignments(studentId, planFeatures);

      // Should create assignments for missing features only
      expect(result).toHaveLength(2);
      expect(result.every(assignment => assignment.tutorId === mockTutor1.id)).toBe(true);
      
      // Should use consistent assignment method
      expect(tutorMatchingService.autoAssignTutorsWithoutNotifications).toHaveBeenCalledTimes(2);
    });
  });
});
