import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { DataSource } from 'typeorm';
import { BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import EmailService from '../../common/services/email.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { DiaryService } from '../diary/diary.service';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { Role } from '../../database/entities/role.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { PasswordReset } from '../../database/entities/password-reset.entity';
import { EmailVerification } from '../../database/entities/email-verification.entity';
import { TutorApproval } from '../../database/entities/tutor-approval.entity';
import { Diary } from '../../database/entities/diary.entity';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let profilePictureService: jest.Mocked<ProfilePictureService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findById: jest.fn(),
            createAdminUser: jest.fn(),
            createTutorUser: jest.fn(),
            createStudentUser: jest.fn(),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendEmail: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: ProfilePictureService,
          useValue: {
            updateProfilePicture: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn().mockReturnValue({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                save: jest.fn(),
                create: jest.fn(),
              },
            }),
          },
        },
        {
          provide: DiaryService,
          useValue: {
            createDiary: jest.fn(),
          },
        },
        {
          provide: NotificationHelperService,
          useValue: {
            sendNotification: jest.fn(),
          },
        },
        {
          provide: DeeplinkService,
          useValue: {
            getWebLink: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Role),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(EmailVerification),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(TutorApproval),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Diary),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService) as any;
    jwtService = module.get<JwtService>(JwtService) as any;
    profilePictureService = module.get<ProfilePictureService>(ProfilePictureService) as any;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        verifyPassword: jest.fn().mockResolvedValue(true),
        isActive: true,
        isConfirmed: true,
        userRoles: [{ role: { name: 'student' } }]
      } as any;

      usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await service.validateUser('TEST001', 'password123');

      expect(result).toEqual(mockUser);
      expect(usersService.findByUserId).toHaveBeenCalledWith('TEST001');
      expect(mockUser.verifyPassword).toHaveBeenCalledWith('password123');
    });

    it('should return null for invalid credentials', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        verifyPassword: jest.fn().mockResolvedValue(false),
        isActive: true,
        isConfirmed: true
      } as any;

      usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await service.validateUser('TEST001', 'wrongpassword');

      expect(result).toBeNull();
    });

    it('should return null for inactive user', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        verifyPassword: jest.fn().mockResolvedValue(true),
        isActive: false,
        isConfirmed: true
      } as any;

      usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await service.validateUser('TEST001', 'password123');

      expect(result).toBeNull();
    });

    it('should return null for unconfirmed user', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        verifyPassword: jest.fn().mockResolvedValue(true),
        isActive: true,
        isConfirmed: false
      } as any;

      usersService.findByUserId.mockResolvedValue(mockUser);

      const result = await service.validateUser('TEST001', 'password123');

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      usersService.findByUserId.mockResolvedValue(null);

      const result = await service.validateUser('NONEXISTENT', 'password123');

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        password: 'hashedpassword',
        userRoles: [{ role: { name: 'student' } }],
        toDto: jest.fn().mockReturnValue({
          id: 'user-1',
          userId: 'TEST001',
          email: '<EMAIL>',
          type: 'student'
        })
      } as any;

      const mockToken = 'jwt-token';
      jwtService.sign.mockReturnValue(mockToken);
      profilePictureService.hasProfilePicture.mockResolvedValue(false);

      const result = await service.login(mockUser);

      expect(result).toHaveProperty('access_token', mockToken);
      expect(result).toHaveProperty('user');
      expect(jwtService.sign).toHaveBeenCalled();
    });

    it('should include profile picture URL if user has one', async () => {
      const mockUser = {
        id: 'user-1',
        userId: 'TEST001',
        email: '<EMAIL>',
        password: 'hashedpassword',
        userRoles: [{ role: { name: 'student' } }],
        toDto: jest.fn().mockReturnValue({
          id: 'user-1',
          userId: 'TEST001',
          email: '<EMAIL>',
          type: 'student'
        })
      } as any;

      const mockToken = 'jwt-token';
      jwtService.sign.mockReturnValue(mockToken);
      profilePictureService.hasProfilePicture.mockResolvedValue(true);
      profilePictureService.getProfilePictureUrl.mockResolvedValue('http://example.com/profile.jpg');

      const result = await service.login(mockUser);

      expect(result.user.profilePictureUrl).toBe('http://example.com/profile.jpg');
      expect(profilePictureService.getProfilePictureUrl).toHaveBeenCalledWith('user-1');
    });
  });

  describe('register', () => {
    it('should register student user successfully', async () => {
      const registerDto = {
        userId: 'STUDENT001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.STUDENT,
        agreedToTerms: true,
        toCreateUserDto: jest.fn()
      } as any;

      const mockUser = {
        id: 'user-1',
        userId: 'STUDENT001',
        email: '<EMAIL>',
        type: UserType.STUDENT
      } as any;

      usersService.createStudentUser.mockResolvedValue(mockUser);

      const result = await service.register(registerDto);

      expect(result).toEqual(mockUser);
      expect(usersService.createStudentUser).toHaveBeenCalled();
    });

    it('should register tutor user successfully', async () => {
      const registerDto = {
        userId: 'TUTOR001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: UserType.TUTOR,
        agreedToTerms: true,
        toCreateUserDto: jest.fn()
      } as any;

      const mockUser = {
        id: 'user-1',
        userId: 'TUTOR001',
        email: '<EMAIL>',
        type: UserType.TUTOR
      } as any;

      usersService.createTutorUser.mockResolvedValue(mockUser);

      const result = await service.register(registerDto);

      expect(result).toEqual(mockUser);
      expect(usersService.createTutorUser).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid user type', async () => {
      const registerDto = {
        userId: 'INVALID001',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '1234567890',
        gender: 'male',
        type: 'invalid' as any,
        agreedToTerms: true,
        toCreateUserDto: jest.fn()
      } as any;

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });
});
