import { Test, TestingModule } from '@nestjs/testing';
import { TutorMatchingService } from './tutor-matching.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { ChatService } from '../chat/chat.service';
import { Logger } from '@nestjs/common';

describe('TutorMatchingService - Assignment Consistency', () => {
  let service: TutorMatchingService;
  let studentTutorMappingRepository: Repository<StudentTutorMapping>;
  let userRepository: Repository<User>;
  let planFeatureRepository: Repository<PlanFeature>;

  // Mock data
  const mockStudent = {
    id: 'student-1',
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    type: UserType.STUDENT,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor1 = {
    id: 'tutor-1',
    name: 'Jane Smith',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockTutor2 = {
    id: 'tutor-2',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    type: UserType.TUTOR,
    isActive: true,
    isConfirmed: true
  } as User;

  const mockFeature1 = {
    id: 'feature-1',
    name: 'English Writing',
    type: 'MODULE',
    description: 'Test feature',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  const mockFeature2 = {
    id: 'feature-2',
    name: 'English Speaking',
    type: 'MODULE',
    description: 'Test feature',
    plans: [],
    toSimpleObject: jest.fn(),
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: null,
    updatedBy: null
  } as unknown as PlanFeature;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TutorMatchingService,
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: ChatService,
          useValue: {
            getOrCreateConversation: jest.fn(),
          },
        },
        {
          provide: NotificationHelperService,
          useValue: {
            notify: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TutorMatchingService>(TutorMatchingService);
    studentTutorMappingRepository = module.get<Repository<StudentTutorMapping>>(
      getRepositoryToken(StudentTutorMapping),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    planFeatureRepository = module.get<Repository<PlanFeature>>(getRepositoryToken(PlanFeature));

    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  describe('getStudentTutorAssignments', () => {
    it('should return all active assignments for a student ordered by assignment date', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        },
        {
          id: 'assignment-2',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature2.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-02'),
          tutor: mockTutor1,
          planFeature: mockFeature2
        }
      ] as StudentTutorMapping[];

      jest.spyOn(studentTutorMappingRepository, 'find').mockResolvedValue(mockAssignments);

      const result = await service.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual(mockAssignments);
      expect(studentTutorMappingRepository.find).toHaveBeenCalledWith({
        where: {
          studentId: mockStudent.id,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor', 'planFeature'],
        order: { assignedDate: 'ASC' }
      });
    });

    it('should return empty array on error', async () => {
      jest.spyOn(studentTutorMappingRepository, 'find').mockRejectedValue(new Error('Database error'));

      const result = await service.getStudentTutorAssignments(mockStudent.id);

      expect(result).toEqual([]);
    });
  });

  describe('getOrSelectPreferredTutor', () => {
    it('should return existing preferred tutor for student with assignments', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        }
      ] as StudentTutorMapping[];

      jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue(mockAssignments);

      const result = await service.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
    });

    it('should select new tutor for student with no assignments', async () => {
      jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue([]);
      jest.spyOn(service, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);

      const result = await service.getOrSelectPreferredTutor(mockStudent.id);

      expect(result).toEqual(mockTutor1);
      expect(service.selectTutorByOverallWorkload).toHaveBeenCalled();
    });
  });

  describe('selectTutorByOverallWorkload', () => {
    it('should select tutor with lowest overall workload', async () => {
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockTutor1, mockTutor2]),
      };

      jest.spyOn(userRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Mock tutor1 has 1 student, tutor2 has 2 students
      jest.spyOn(studentTutorMappingRepository, 'count')
        .mockResolvedValueOnce(1) // tutor1 workload
        .mockResolvedValueOnce(2); // tutor2 workload

      const result = await service.selectTutorByOverallWorkload();

      expect(result).toEqual(mockTutor1); // Should select tutor with lower workload
    });
  });

  describe('Core Assignment Methods', () => {
    describe('autoAssignTutorsWithoutNotifications', () => {
      it('should use preferred tutor for new student assignments', async () => {
        // Mock no existing assignments for student
        jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue([]);
        jest.spyOn(service, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);

        // Mock feature and student validation
        jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature1);
        jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);

        // Mock no existing assignment for this feature
        jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

        // Mock assignment creation
        const mockMapping = {
          id: 'assignment-1',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          notes: 'Auto-assigned by system with preferred tutor logic'
        } as StudentTutorMapping;

        jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping);
        jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping);

        const result = await service.autoAssignTutorsWithoutNotifications({
          planFeatureId: mockFeature1.id,
          studentIds: [mockStudent.id],
          reassignExisting: false
        });

        expect(result).toHaveLength(1);
        expect(result[0].tutorId).toBe(mockTutor1.id);
        expect(service.getStudentTutorAssignments).toHaveBeenCalledWith(mockStudent.id);
      });

      it('should use existing preferred tutor for students with assignments', async () => {
        // Mock existing assignment
        const existingAssignment = {
          id: 'existing-assignment',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: 'other-feature',
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        } as StudentTutorMapping;

        jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue([existingAssignment]);

        // Mock feature and student validation
        jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature2);
        jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);

        // Mock no existing assignment for this specific feature
        jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

        // Mock assignment creation with same tutor
        const mockMapping = {
          id: 'assignment-2',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id, // Same tutor as existing assignment
          planFeatureId: mockFeature2.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          notes: 'Auto-assigned by system with preferred tutor logic'
        } as StudentTutorMapping;

        jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping);
        jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping);

        const result = await service.autoAssignTutorsWithoutNotifications({
          planFeatureId: mockFeature2.id,
          studentIds: [mockStudent.id],
          reassignExisting: false
        });

        expect(result).toHaveLength(1);
        expect(result[0].tutorId).toBe(mockTutor1.id); // Same tutor as existing assignment
        expect(service.selectTutorByOverallWorkload).not.toHaveBeenCalled(); // Should not select new tutor
      });

      it('should handle fallback when preferred tutor selection fails', async () => {
        // Mock preferred tutor selection failure
        jest.spyOn(service, 'getStudentTutorAssignments').mockRejectedValue(new Error('Database error'));
        jest.spyOn(service, 'selectTutorForModule').mockResolvedValue(mockTutor2);

        // Mock feature and student validation
        jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature1);
        jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);
        jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

        // Mock assignment creation with fallback tutor
        const mockMapping = {
          id: 'assignment-fallback',
          studentId: mockStudent.id,
          tutorId: mockTutor2.id,
          planFeatureId: mockFeature1.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          notes: 'Auto-assigned by system with preferred tutor logic'
        } as StudentTutorMapping;

        jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping);
        jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping);

        const result = await service.autoAssignTutorsWithoutNotifications({
          planFeatureId: mockFeature1.id,
          studentIds: [mockStudent.id],
          reassignExisting: false
        });

        expect(result).toHaveLength(1);
        expect(result[0].tutorId).toBe(mockTutor2.id); // Fallback tutor used
        expect(service.selectTutorForModule).toHaveBeenCalledWith(mockFeature1.id);
      });
    });

    describe('autoAssignTutorsWithPreference', () => {
      it('should respect exclude list when preferred tutor is excluded', async () => {
        // Mock existing assignment with tutor1
        const existingAssignment = {
          id: 'existing-assignment',
          studentId: mockStudent.id,
          tutorId: mockTutor1.id,
          planFeatureId: 'other-feature',
          status: MappingStatus.ACTIVE,
          assignedDate: new Date('2024-01-01'),
          tutor: mockTutor1,
          planFeature: mockFeature1
        } as StudentTutorMapping;

        jest.spyOn(service, 'getStudentTutorAssignments').mockResolvedValue([existingAssignment]);
        jest.spyOn(service, 'selectTutorForModuleWithPreference').mockResolvedValue(mockTutor2);

        // Mock feature and student validation
        jest.spyOn(planFeatureRepository, 'findOne').mockResolvedValue(mockFeature2);
        jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockStudent);
        jest.spyOn(studentTutorMappingRepository, 'findOne').mockResolvedValue(null);

        // Mock assignment creation with alternative tutor
        const mockMapping = {
          id: 'assignment-alternative',
          studentId: mockStudent.id,
          tutorId: mockTutor2.id, // Different tutor due to exclude list
          planFeatureId: mockFeature2.id,
          status: MappingStatus.ACTIVE,
          assignedDate: new Date(),
          notes: 'Auto-assigned by system with preferred tutor logic'
        } as StudentTutorMapping;

        jest.spyOn(studentTutorMappingRepository, 'create').mockReturnValue(mockMapping);
        jest.spyOn(studentTutorMappingRepository, 'save').mockResolvedValue(mockMapping);

        const result = await service.autoAssignTutorsWithPreference({
          planFeatureId: mockFeature2.id,
          studentIds: [mockStudent.id],
          reassignExisting: false,
          excludeTutorIds: [mockTutor1.id] // Exclude preferred tutor
        });

        expect(result).toHaveLength(1);
        expect(result[0].tutorId).toBe(mockTutor2.id); // Alternative tutor used
        expect(service.selectTutorForModuleWithPreference).toHaveBeenCalledWith(
          mockFeature2.id,
          [mockTutor1.id]
        );
      });
    });
  });

  describe('Consistent Assignment Integration', () => {
    it('should demonstrate consistent tutor assignment across features', async () => {
      // Setup: New student with no existing assignments
      jest.spyOn(service, 'getStudentTutorAssignments')
        .mockResolvedValueOnce([]) // First call - no assignments
        .mockResolvedValueOnce([   // Second call - has first assignment
          {
            id: 'assignment-1',
            studentId: mockStudent.id,
            tutorId: mockTutor1.id,
            planFeatureId: mockFeature1.id,
            status: MappingStatus.ACTIVE,
            assignedDate: new Date('2024-01-01'),
            tutor: mockTutor1,
            planFeature: mockFeature1
          } as StudentTutorMapping
        ]);

      jest.spyOn(service, 'selectTutorByOverallWorkload').mockResolvedValue(mockTutor1);

      // Test first feature assignment
      const firstTutor = await service.getOrSelectPreferredTutor(mockStudent.id);
      expect(firstTutor).toEqual(mockTutor1);

      // Test second feature assignment - should get same tutor
      const secondTutor = await service.getOrSelectPreferredTutor(mockStudent.id);
      expect(secondTutor).toEqual(mockTutor1);

      // Verify consistency
      expect(firstTutor.id).toBe(secondTutor.id);
    });
  });
});
