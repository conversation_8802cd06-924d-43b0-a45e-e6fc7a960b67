import {
  OmitType,
  ApiProperty,
  ApiPropertyOptional,
  PartialType,
} from '@nestjs/swagger';
import {
  ArrayMinSize,
  ValidateNested,
  IsArray,
  IsEnum,
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsBoolean,
  IsUUID,
  IsDate,
  IsObject,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';
import { QAMissionFrequency } from '../entities/qa-mission-goal.entity';
import { QASubmissionStatus } from '../entities/qa-task-submissions.entity';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { Optional } from '@nestjs/common';

export class QAMissionPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Mission frequency',
    enum: QAMissionFrequency,
    required: true,
  })
  @IsEnum(QAMissionFrequency)
  timeFrequency?: QAMissionFrequency;
  
  @ApiProperty({
    description: 'Search term for mission title',
    required: false
  })
  @IsOptional()
  @IsString()
  title?: string;
  
  @ApiProperty({
    description: 'Search for week number or month number',
    required: false
  })
  @IsOptional()
  weekOrMonth?: number;
  
  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'week', 'month', 'title'],
    required: false
  })
  @IsOptional()
  @IsString()
  @IsIn(['createdAt', 'updatedAt', 'week', 'month', 'title'], { each: false })
  override sortBy?: string = 'createdAt';
}

export class QATaskMetaDataDto {
  @ApiProperty({
    example: '1',
    description: 'The week number',
    required: false,
  })
  @IsOptional()
  @IsString()
  week?: string;

  @ApiProperty({
    example: '4',
    description: 'The month number',
    required: false,
  })
  @IsOptional()
  @IsString()
  month?: string;

  @ApiProperty({
    example: '2025',
    description: 'The year',
    required: false,
  })
  @IsOptional()
  @IsString()
  year?: string;
}

export class QATaskDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the task',
  })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({
    example: 'Week 1: Basic Grammar Questions',
    description: 'The title of the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Title is required' })
  title: string;

  @ApiProperty({
    example: 'Answer the following questions about basic English grammar',
    description: 'The description of the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @ApiProperty({
    example: 100,
    description: 'The minimum word limit for the task',
  })
  @IsNumber()
  @IsNotEmpty({ message: 'Minimum word limit is required' })
  wordLimitMinimum: number;

  @ApiProperty({
    example: 300,
    description: 'The maximum word limit for the task',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  wordLimitMaximum?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the task is active',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    example: 1,
    description: 'The time period unit for the task',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  timePeriodUnit?: number;

  @ApiProperty({
    example: 1,
    description: 'The deadline for the task (in days)',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  deadline?: number;

  @ApiProperty({
    example: 'Provide detailed explanations and include relevant examples',
    description: 'Instructions for completing the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Instructions are required' })
  instructions: string;

  @ApiProperty({
    type: QATaskMetaDataDto,
    description: 'Meta data for the task',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => QATaskMetaDataDto)
  metaData?: QATaskMetaDataDto;
}

export class QATaskProgressDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the task',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 75,
    description: 'The progress percentage of the task',
  })
  @IsNumber()
  progress: number;
}

export class CreateQATaskDto {
    @ApiProperty({
      example: 'Week 1: Basic Grammar Questions',
      description: 'The title of the task',
    })
    @IsString()
    @IsNotEmpty({ message: 'Title is required' })
    title: string;
  
    @ApiProperty({
      example: 'Answer the following questions about basic English grammar...',
      description: 'The description of the task',
    })
    @IsString()
    @IsNotEmpty({ message: 'Description is required' })
    description: string;
  
    @ApiProperty({
      example: 500,
      description: "The minimum word limit for the task"
    })
    @IsNumber()
    @Min(1, { message: "Word limit minimum must be at least 1" })
    @Type(() => Number)
    @IsNotEmpty({ message: "Word limit minimum is required" })
    wordLimitMinimum: number;
  
    @ApiProperty({
      example: 1000,
      description: "The maximum word limit for the task",
      required: false
    })
    @IsNumber()
    // @Min(1, { message: "Word limit minimum must be at least 1" })
    @Type(() => Number)
    @IsOptional()
    wordLimitMaximum?: number;
  
    @ApiProperty({ 
      example: 7,
      description: 'Time period unit for the task', 
      default: 1,
      required: false
    })
    @IsNumber()
    @Type(() => Number)
    @IsOptional()
    timePeriodUnit?: number;
    
    @ApiProperty({
      example: 1,
      description: "The deadline for the task (in days)",
      required: false
    })
    @IsNumber()
    @Type(() => Number)
    @IsOptional()
    deadline?: number;
  
    @ApiProperty({
      example: 'Please provide detailed answers with examples',
      description: 'Instructions for completing the task',
    })
    @IsString()
    @IsNotEmpty({ message: 'Instructions are required' })
    instructions: string;
    
    @ApiProperty({
      example: true,
      description: "Whether the task is active",
      default: true,
      required: false
    })
    @IsBoolean()
    @Type(() => Boolean)
    @IsOptional()
    isActive?: boolean;
    
    @ApiProperty({
      type: QATaskMetaDataDto,
      description: 'Meta data for the task',
      required: false,
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => QATaskMetaDataDto)
    metaData?: QATaskMetaDataDto;
}

export class CreateQAMissionDto {
    @ApiProperty({
      example: 'weekly',
      description: 'The frequency of the mission',
      enum: QAMissionFrequency,
    })
    @IsEnum(QAMissionFrequency)
    @IsNotEmpty({ message: 'Time frequency is required' })
    timeFrequency: QAMissionFrequency;
    
    @ApiProperty({
      description: 'The tasks for this mission',
      type: [CreateQATaskDto],
    })
    @IsArray()
    @ArrayMinSize(1, { message: 'At least one task is required' })
    @ValidateNested({ each: true })
    @Type(() => CreateQATaskDto)
    tasks: CreateQATaskDto[];
}

export class QAMissionTaskDto {
  @ApiProperty({
      example: 'Week 1: Basic Grammar Questions',
      description: 'The title of the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Title is required' })
  title: string;

  @ApiProperty({
    example: 'Answer the following questions about basic English grammar...',
    description: 'The description of the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @ApiProperty({
    example: 500,
    description: "The minimum word limit for the task"
  })
  @IsNumber()
  @Min(1, { message: "Word limit minimum must be at least 1" })
  @Type(() => Number)
  @IsNotEmpty({ message: "Word limit minimum is required" })
  wordLimitMinimum: number;

  @ApiProperty({
    example: 1000,
    description: "The maximum word limit for the task",
    required: false
  })
  @IsNumber()
  // @Min(1, { message: "Word limit minimum must be at least 1" })
  @Type(() => Number)
  @IsOptional()
  wordLimitMaximum?: number;

  @ApiProperty({
    example: 1,
    description: "The deadline for the task (in days)",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  deadline?: number;

  @ApiProperty({
    example: 'Please provide detailed answers with examples',
    description: 'Instructions for completing the task',
  })
  @IsString()
  @IsNotEmpty({ message: 'Instructions are required' })
  instructions: string;

  @ApiProperty({
    example: true,
    description: "Whether the task is active",
    default: true,
    required: false
  })
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    example: 1,
    description: "The sequence",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  sequence: number;

  @ApiProperty({
    example: 1,
    description: "The sequence",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  totalScore: number;

  @ApiProperty({
    type: QATaskMetaDataDto,
    description: 'Meta data for the task',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => QATaskMetaDataDto)
  metaData?: QATaskMetaDataDto;
}

export class CreateAllQAMissionDto {
    @ApiProperty({
      example: 'weekly',
      description: 'The frequency of the mission',
      enum: QAMissionFrequency,
    })
    @IsEnum(QAMissionFrequency)
    @IsNotEmpty({ message: 'Time frequency is required' })
    timeFrequency: QAMissionFrequency;

    @ApiPropertyOptional({
      example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
      description: 'Optional week ID if the mission is weekly',
    })
    @IsOptional()
    @IsUUID()
    weekId?: string;

    @ApiPropertyOptional({
      example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
      description: 'Optional month ID if the mission is monthly',
    })
    @IsOptional()
    @IsUUID()
    monthId?: string;
    
    @ApiProperty({
      description: 'The tasks for this mission',
      type: [QAMissionTaskDto],
    })
    @IsArray()
    @ArrayMinSize(1, { message: 'At least one task is required' })
    @ValidateNested({ each: true })
    @Type(() => QAMissionTaskDto)
    tasks: QAMissionTaskDto[];
}

class UpdateQAMissionTaskDto extends PartialType(CreateQATaskDto) {
    @ApiPropertyOptional({
      description: 'ID of the task if updating an existing one',
    })
    @IsString()
    @IsOptional()
    id?: string;
}

class QAMissionPropertiesDto extends OmitType(CreateQAMissionDto, ['tasks'] as const) {}

class PartialQAMissionPropertiesDto extends PartialType(QAMissionPropertiesDto) {}

class SubmissionMetadataDto {
    @ApiProperty({
      example: "Mozilla/5.0...",
      description: "Browser information",
      required: false
    })
    @IsString()
    @IsOptional()
    browserInfo?: string;
  
    @ApiProperty({
      example: "***********",
      description: "IP Address",
      required: false
    })
    @IsString()
    @IsOptional()
    ipAddress?: string;
  
    @ApiProperty({
      example: 1,
      description: "Number of submission attempts",
      required: false
    })
    @IsNumber()
    @IsOptional()
    submissionAttempts?: number;
  
    @ApiProperty({
      example: "2023-01-01T00:00:00.000Z",
      description: "Last draft saved timestamp",
      required: false
    })
    @IsDate()
    @IsOptional()
    lastDraftSavedAt?: Date;
  
    @ApiProperty({
      example: 300,
      description: "Time spent in seconds",
      required: false
    })
    @IsNumber()
    @IsOptional()
    timeSpent?: number;
  
    @ApiProperty({
      example: 50,
      description: "Word count difference from previous version",
      required: false
    })
    @IsNumber()
    @IsOptional()
    wordCountDiff?: number;
  
    @ApiProperty({
      description: "Content change analysis",
      required: false
    })
    @IsObject()
    @IsOptional()
    contentChanges?: {
      paragraphsAdded?: number;
      paragraphsRemoved?: number;
      significantChanges?: boolean;
    };
  
    @ApiProperty({
      example: "Check grammar in paragraph 2",
      description: "Reviewer notes",
      required: false
    })
    @IsString()
    @IsOptional()
    reviewerNotes?: string;
  
    @ApiProperty({
      example: 0.95,
      description: "AI content detection score",
      required: false
    })
    @IsNumber()
    @IsOptional()
    aiDetectionScore?: number;
}

export class QATaskSubmissionHistoryDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the submission',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 'This is my answer...',
    description: 'The submitted answer',
  })
  @IsString()
  content: string;

  @ApiProperty({
    example: 500,
    description: "Word count of the Q&A"
  })
  @IsNumber()
  @Min(0)
  wordCount: number;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Submission date"
  })
  @IsDate()
  submissionDate: Date;

  @ApiProperty({
    example: 1,
    description: "Sequence number of the submission"
  })
  @IsNumber()
  @Min(1)
  sequenceNumber: number;

  @ApiProperty({
    example: 'Good work, but needs improvement in...',
    description: 'Feedback on the submission',
    required: false,
  })
  @IsString()
  @IsOptional()
  feedback?: string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Review date",
    required: false
  })
  @IsDate()
  @IsOptional()
  reviewedAt?: Date;

  @ApiProperty({
    type: SubmissionMetadataDto,
    description: "Additional metadata about the submission"
  })
  @ValidateNested()
  @Type(() => SubmissionMetadataDto)
  @IsOptional()
  metaData?: SubmissionMetadataDto;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "ID of the previous revision",
    required: false
  })
  @IsUUID()
  @IsOptional()
  previousRevisionId?: string;

  // @ApiProperty({
  //   example: 1,
  //   description: 'The revision number of this submission',
  // })
  // @IsNumber()
  // @Min(1)
  // revisionNumber: number;

  // @ApiProperty({
  //   example: '2023-01-01T00:00:00.000Z',
  //   description: 'Submission date',
  // })
  // @IsDate()
  // submittedAt: Date;

  // @ApiProperty({
  //   enum: QASubmissionStatus,
  //   description: 'Status of the submission',
  // })
  // @IsEnum(QASubmissionStatus)
  // status: QASubmissionStatus;
}
  
export class QATaskSubmissionDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the submission',
  })
  @IsUUID()
  //@IsNotEmpty({ message: "Submission ID is required" })
  id: string;

  @ApiProperty({
    example: "draft",
    description: "The status of the submission",
    enum: QASubmissionStatus
  })
  @IsEnum(QASubmissionStatus)
  @IsNotEmpty({ message: "Submission status is required" })
  status: QASubmissionStatus;

  @ApiProperty({
    example: 1,
    description: "Current Revision number of the submission"
  })
  @IsNumber()
  @IsNotEmpty({ message: "Revision number is required" })
  @Min(1, { message: "Revision number must be at least 1" })
  currentRevision: number;

  @ApiProperty({
    example: true,
    description: "Whether the submission is active",
    default: true,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    type: [QATaskSubmissionHistoryDto],
    description: 'History of all submission revisions',
    isArray: true,
    required: true
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QATaskSubmissionHistoryDto)
  submissionHistory: QATaskSubmissionHistoryDto[];

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The task ID to submit for"
  })
  @IsUUID()
  taskId: string;

  @ApiProperty({
    example: 'This is my answer...',
    description: 'The current answer',
        isArray: true,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  // @ApiProperty({
  //   example: "123e4567-e89b-12d3-a456-************",
  //   description: "created by user ID",
  //   required: false
  // })
  // @IsNotEmpty({ message: "Created by user ID is required" })
  // updatedBy: string;

  // @ApiProperty({
  //   example: "2023-01-01T00:00:00.000Z",
  //   description: "The date when the submission was created"
  // })
  // @IsDate()
  // @IsNotEmpty({ message: "Created date is required" })
  // createdAt: Date;

  // @ApiProperty({
  //   example: "2023-01-01T00:00:00.000Z",
  //   description: "The date when the submission was last updated"
  // })
  // @IsDate()
  // @IsNotEmpty({ message: "Updated date is required" })
  // updatedAt: Date;
}

export class QAMissionResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the mission',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'weekly',
    description: 'The frequency of the mission',
    enum: QAMissionFrequency,
  })
  @IsEnum(QAMissionFrequency)
  @IsNotEmpty()
  timeFrequency: QAMissionFrequency;

  @ApiProperty({
    example: 1,
    description: "The sequence number of the mission",
    default: 1,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  sequenceNumber?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the mission is active',
    default: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiPropertyOptional({
    example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
    description: 'Optional week ID if the mission is weekly',
  })
  @IsOptional()
  @IsUUID()
  weekId?: string;

  @ApiPropertyOptional({
    example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
    description: 'Optional month ID if the mission is monthly',
  })
  @IsOptional()
  @IsUUID()
  monthId?: string;
  
  @ApiProperty({
    type: [CreateQATaskDto],
    description: 'The tasks associated with this mission',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateQATaskDto)
  tasks: CreateQATaskDto[];

  @ApiProperty({
    type: [QATaskProgressDto],
    description: "The progress of each task in the mission"
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QATaskProgressDto)
  @IsOptional()
  taskProgress?: {
    id: string;
    progress: number;
  }[];
}

export class QAMissionListResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the mission',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'weekly',
    description: 'The frequency of the mission',
    enum: QAMissionFrequency,
  })
  @IsEnum(QAMissionFrequency)
  @IsNotEmpty()
  timeFrequency: QAMissionFrequency;

  @ApiProperty({
    example: 1,
    description: "Number of Tasks in the mission",
    default: 0,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  totalTask?: number;

  @ApiProperty({
    example: 1,
    description: "The sequence number of the mission",
    default: 1,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  sequenceNumber?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the mission is active',
    default: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    example: 'Week-01',
    description: 'mission title',
  })
  title: string;

  @ApiPropertyOptional({
    example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
    description: 'Optional week ID if the mission is weekly',
  })
  @IsOptional()
  @IsUUID()
  weekId?: string;

  @ApiPropertyOptional({
    example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
    description: 'Optional month ID if the mission is monthly',
  })
  @IsOptional()
  @IsUUID()
  monthId?: string;
}


export class NewQAMissionResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the mission',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'weekly',
    description: 'The frequency of the mission',
    enum: QAMissionFrequency,
  })
  @IsEnum(QAMissionFrequency)
  @IsNotEmpty()
  timeFrequency: QAMissionFrequency;

  @ApiProperty({
    example: 1,
    description: "The sequence number of the mission",
    default: 1,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  sequenceNumber?: number;

    @ApiPropertyOptional({
    example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
    description: 'Optional week ID if the mission is weekly',
  })
  @IsOptional()
  @IsUUID()
  weekId?: string;

  @ApiPropertyOptional({
    example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
    description: 'Optional month ID if the mission is monthly',
  })
  @IsOptional()
  @IsUUID()
  monthId?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the mission is active',
    default: true,
  })
  @IsBoolean()
  isActive: boolean;
  
  @ApiProperty({
    type: [CreateQATaskDto],
    description: 'The tasks associated with this mission',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateQATaskDto)
  tasks: CreateQATaskDto[];

  @ApiProperty({
    type: [QATaskProgressDto],
    description: "The progress of each task in the mission"
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QATaskProgressDto)
  @IsOptional()
  taskProgress?: {
    id: string;
    progress: number;
  }[];
}

export class AllQAMissionResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The unique identifier of the mission',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: 'weekly',
    description: 'The frequency of the mission',
    enum: QAMissionFrequency,
  })
  @IsEnum(QAMissionFrequency)
  @IsNotEmpty()
  timeFrequency: QAMissionFrequency;

  @ApiProperty({
    example: 1,
    description: "The sequence number of the mission",
    default: 1,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  sequenceNumber?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the mission is active',
    default: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiPropertyOptional({
    example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
    description: 'Optional week ID if the mission is weekly',
  })
  @IsOptional()
  @IsUUID()
  weekId?: string;

  @ApiPropertyOptional({
    example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
    description: 'Optional month ID if the mission is monthly',
  })
  @IsOptional()
  @IsUUID()
  monthId?: string;
  
  @ApiProperty({
    type: [CreateQATaskDto],
    description: 'The tasks associated with this mission',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateQATaskDto)
  tasks: CreateQATaskDto[];

  @ApiProperty({
    type: [QATaskProgressDto],
    description: "The progress of each task in the mission"
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QATaskProgressDto)
  @IsOptional()
  taskProgress?: {
    id: string;
    progress: number;
  }[];
}

export class UpdateQATaskDto {
  @ApiProperty({
    description: 'Task ID (required for updating existing tasks)',
    required: false
  })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiProperty({
    example: 'Grammar Questions',
    description: 'The title of the task',
    required: false
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  title?: string;

  @ApiProperty({
    example: 'Answer the following questions about English grammar',
    description: 'The description of the task',
    required: false
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiProperty({
    example: 200,
    description: 'The minimum word limit for the task',
    required: false
  })
  @IsOptional()
  @IsNumber()
  wordLimitMinimum?: number;

  @ApiProperty({
    example: 500,
    description: 'The maximum word limit for the task',
    required: false
  })
  @IsOptional()
  @IsNumber()
  wordLimitMaximum?: number;

  @ApiProperty({
    example: 7,
    description: 'Deadline in days',
    required: false
  })
  @IsOptional()
  @IsNumber()
  deadline?: number;

  @ApiProperty({
    example: 'Provide examples for each answer',
    description: 'Instructions for completing the task',
    required: false
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  instructions?: string;

  // @ApiProperty({
  //   example: true,
  //   description: 'Whether the task is active',
  //   required: false
  // })
  // @IsOptional()
  // @IsBoolean()
  // isActive?: boolean;

  // @ApiProperty({
  //   example: 1,
  //   description: "The sequence",
  //   required: false
  // })
  // @IsNumber()
  // @Type(() => Number)
  // @IsOptional()
  // sequence: number;

  @ApiProperty({
    example: 1,
    description: "The sequence",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  totalScore: number;
}

// export class UpdateQAMissionDto extends PartialQAMissionPropertiesDto {
//   @ApiPropertyOptional({
//     type: [UpdateQAMissionTaskDto],
//     description: 'Tasks to be updated or added to the mission',
//   })
//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => UpdateQAMissionTaskDto)
//   @IsOptional()
//   tasks?: UpdateQAMissionTaskDto[];
// }

export class UpdateQAMissionDto {
  @ApiProperty({
    example: 'weekly',
    description: 'The frequency of the mission',
    enum: QAMissionFrequency,
    required: false
  })
  @IsOptional()
  @IsEnum(QAMissionFrequency)
  timeFrequency?: QAMissionFrequency;

    @ApiPropertyOptional({
      example: 'e3b0c442-98fc-1fcf-b9d1-3b5f5b5a5e5a',
      description: 'Optional week ID if the mission is weekly',
    })
    @IsOptional()
    @IsUUID()
    weekId?: string;

    @ApiPropertyOptional({
      example: 'b1a97f99-f439-4c5e-9d7c-d8e51a2d6d1f',
      description: 'Optional month ID if the mission is monthly',
    })
    @IsOptional()
    @IsUUID()
    monthId?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the mission is active',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'The tasks to update or create',
    type: [UpdateQATaskDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateQATaskDto)
  tasks?: UpdateQATaskDto[];
}

// export class UpdateQATaskDto {
//   @IsOptional()
//   @IsString()
//   title?: string;

//   @IsOptional()
//   @IsString()
//   description?: string;

//   @IsOptional()
//   @IsInt()
//   wordLimitMinimum?: number;

//   @IsOptional()
//   @IsInt()
//   wordLimitMaximum?: number;

//   @IsOptional()
//   @IsInt()
//   deadline?: number;

//   @IsOptional()
//   @IsString()
//   instructions?: string;

//   @IsOptional()
//   @IsBoolean()
//   isActive?: boolean;

//   @IsOptional()
//   @IsInt()
//   sequence?: number;

//   @IsOptional()
//   @IsInt()
//   totalScore?: number;

//   @IsOptional()
//   @IsUUID()
//   missionId?: string;
// }

export class CreateQATaskSubmissionMarkingDto {
  @ApiProperty({
    example: 10,
    description: "The total points for the task"
  })
  @IsNumber()
  @IsOptional()
  score: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission ID to mark"
  })
  @IsUUID()
  submissionId: string;
}

export class UpdateQATaskSubmissionMarkingDto {
  @ApiProperty({
    example: 10,
    description: "The total points for the task"
  })
  @IsNumber()
  @IsOptional()
  points?: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;
}

export class QATaskSubmissionMarkingDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the marking"
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 10,
    description: "The total points for the task"
  })
  @IsNumber()
  score: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission ID to mark"
  })
  @IsUUID()
  submissionId: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission history ID to mark"
  })
  @IsUUID()
  submissionHistoryId: string;
}

export class StartQATaskDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The ID of the QA task to start'
  })
  @IsUUID()
  @IsNotEmpty({ message: "Task ID is required" })
  taskId: string;
}

export class CreateQATaskSubmissionDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The ID of the QA task'
  })
  @IsUUID()
  @IsNotEmpty()
  taskId: string;

  @ApiProperty({
    example: 'This is my answer to the QA task',
    description: 'The content of the QA submission'
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    example: "70",
    description: "Words written by student until now"
  })
  @IsNotEmpty({ message: "Count is required" })
  wordCount: number;

  @ApiProperty({
    example: { timeSpent: 120 },
    description: 'Additional metadata for the submission',
    required: false
  })
  @IsOptional()
  @IsObject()
  metaData?: Record<string, any>;
}

export class QATaskSubmissionUpdate {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The Submission ID to update'
  })
  @IsUUID()
  @IsNotEmpty({ message: "Task ID is required" })
  submissionId: string;

  @ApiProperty({
    example: 'This is my updated QA submission',
    description: 'The updated content of the QA submission'
  })
  @IsString()
  @IsNotEmpty({ message: "Content is required" })
  content: string;

  @ApiProperty({
    example: "70",
    description: "Words written by student until now"
  })
  @IsNotEmpty({ message: "Count is required" })
  wordCount: number;

  @ApiProperty({
    example: { timeSpent: 120 },
    description: 'Additional metadata for the submission',
    required: false
  })
  @IsOptional()
  @IsObject()
  metaData?: Record<string, any>;
}