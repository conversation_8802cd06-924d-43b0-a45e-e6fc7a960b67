import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import LoggerService from '../../common/services/logger.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';



describe('UsersController', () => {
  let controller: UsersController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            findById: jest.fn(),
            updateProfile: jest.fn(),
            updateProfilePicture: jest.fn(),
            calculateAge: jest.fn(),
            createAdminUser: jest.fn(),
            createTutorUser: jest.fn(),
            createStudentUser: jest.fn(),
            getUsers: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
            audit: jest.fn(),
            operation: jest.fn(),
          },
        },
        {
          provide: ProfilePictureService,
          useValue: {
            updateProfilePicture: jest.fn(),
            deleteProfilePicture: jest.fn(),
            getProfilePictureUrl: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  let usersService: any;

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated list of users', async () => {
      const mockPagedList = {
        items: [
          {
            id: 'user-1',
            userId: 'TEST001',
            name: 'Test User',
            email: '<EMAIL>',
            type: 'student'
          }
        ],
        totalItems: 1,
        currentPage: 1,
        totalPages: 1
      };

      usersService.findAllPaginated.mockResolvedValue(mockPagedList);

      const filterDto = { page: 1, limit: 10 };
      const result = await controller.findAll(filterDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPagedList);
      expect(result.message).toBe('Users retrieved successfully');
      expect(usersService.findAllPaginated).toHaveBeenCalledWith(filterDto);
    });
  });

  describe('getAdminUsers', () => {
    it('should return paginated list of admin users', async () => {
      const mockPagedList = {
        items: [
          {
            id: 'admin-1',
            userId: 'ADMIN001',
            name: 'Admin User',
            email: '<EMAIL>',
            type: 'admin'
          }
        ],
        totalItems: 1,
        currentPage: 1,
        totalPages: 1
      };

      usersService.findAllAdminUsers.mockResolvedValue(mockPagedList);

      const filterDto = { page: 1, limit: 10 };
      const result = await controller.getAdminUsers(filterDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPagedList);
      expect(result.message).toBe('Admin users retrieved successfully');
      expect(usersService.findAllAdminUsers).toHaveBeenCalledWith(filterDto);
    });
  });

  describe('getTutorUsers', () => {
    it('should return paginated list of tutor users', async () => {
      const mockPagedList = {
        items: [
          {
            id: 'tutor-1',
            userId: 'TUTOR001',
            name: 'Tutor User',
            email: '<EMAIL>',
            type: 'tutor'
          }
        ],
        totalItems: 1,
        currentPage: 1,
        totalPages: 1
      };

      usersService.findAllTutorUsers.mockResolvedValue(mockPagedList);

      const filterDto = { page: 1, limit: 10 };
      const result = await controller.getTutorUsers(filterDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPagedList);
      expect(result.message).toBe('Tutor users retrieved successfully');
      expect(usersService.findAllTutorUsers).toHaveBeenCalledWith(filterDto);
    });
  });

  describe('getStudentUsers', () => {
    it('should return paginated list of student users', async () => {
      const mockPagedList = {
        items: [
          {
            id: 'student-1',
            userId: 'STUDENT001',
            name: 'Student User',
            email: '<EMAIL>',
            type: 'student'
          }
        ],
        totalItems: 1,
        currentPage: 1,
        totalPages: 1
      };

      usersService.findAllStudentUsers.mockResolvedValue(mockPagedList);

      const filterDto = { page: 1, limit: 10 };
      const result = await controller.getStudentUsers(filterDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPagedList);
      expect(result.message).toBe('Student users retrieved successfully');
      expect(usersService.findAllStudentUsers).toHaveBeenCalledWith(filterDto);
    });
  });

  describe('calculateAge', () => {
    it('should calculate age correctly', async () => {
      const mockAgeResponse = { age: 33 };
      usersService.calculateAge.mockReturnValue(mockAgeResponse);

      const calculateAgeDto = { dateOfBirth: '1990-01-01' };
      const result = await controller.calculateAge(calculateAgeDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAgeResponse);
      expect(result.message).toBe('Age calculated successfully');
      expect(usersService.calculateAge).toHaveBeenCalledWith('1990-01-01');
    });
  });

  describe('createAdmin', () => {
    it('should create admin user successfully', async () => {
      const mockUserResponse = {
        id: 'admin-1',
        userId: 'ADMIN001',
        name: 'Admin User',
        email: '<EMAIL>',
        type: 'admin',
        temporaryPassword: 'TempPass123!'
      };

      usersService.createAdminUserByAdmin.mockResolvedValue(mockUserResponse);

      const createAdminDto = {
        userId: 'ADMIN001',
        email: '<EMAIL>'
      };

      const mockReq = { user: { sub: 'admin-id' } };
      const result = await controller.createAdmin(mockReq, createAdminDto);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUserResponse);
      expect(result.message).toBe('Admin user created successfully and email sent');
      expect(usersService.createAdminUserByAdmin).toHaveBeenCalledWith(createAdminDto, 'admin-id');
    });
  });
});
