import { OmitType, ApiProperty, ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import {
  ArrayMinSize,
  ValidateNested,
  IsArray,
  IsEnum,
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsBoolean,
  IsUUID,
  IsDate,
  IsObject,
  IsIn,
  Max
} from "class-validator";
import { Type } from "class-transformer";
import { MissionFrequency } from "../entities/essay-mission.entity";
import { SubmissionStatus } from "../entities/essay-task-submissions.entity";
import { PaginationDto } from "src/common/models/pagination.dto";
import { SkinScopeType } from "../entities/essay-preferences.entity";
import { DiarySkinResponseDto } from "./diary.dto";
import { EssayModuleSkinPreference } from "../entities/essay-preferences.entity";

export class MissionPaginationDto {
  @ApiProperty({
    description: 'Mission frequency',
    enum: MissionFrequency,
    required: true
  })
  @IsEnum(MissionFrequency)
  timeFrequency: MissionFrequency = MissionFrequency.WEEKLY;

  @ApiProperty({
    description: 'Search term for mission title',
    example: 'Weekly Essay',
    required: false
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Search for week number or month number',
    example: 1,
    required: false
  })
  @IsOptional()
  weekOrMonth?: number;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'week', 'month', 'title'],
    required: false
  })
  @IsOptional()
  @IsString()
  @IsIn(['createdAt', 'updatedAt', 'week', 'month', 'title'], { each: false })
  sortBy?: string;

  @ApiProperty({
      description: 'Page number (1-based)',
      example: 1,
      default: 1,
      required: false
    })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;
  
    @ApiProperty({
      description: 'Number of items per page',
      example: 10,
      default: 10,
      required: false
    })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 10;
  
    @ApiProperty({
      description: 'Sort direction',
      example: 'DESC',
      enum: ['ASC', 'DESC'],
      required: false
    })
    @IsOptional()
    @IsString()
    @IsIn(['ASC', 'DESC'])
    sortDirection?: 'ASC' | 'DESC';
}

export class TaskMetaDataDto {
  @ApiProperty({
    example: "1",
    description: "The week number",
    required: false
  })
  @IsOptional()
  @IsString()
  week?: string;

  @ApiProperty({
    example: "4",
    description: "The month number",
    required: false
  })
  @IsOptional()
  @IsString()
  month?: string;

  @ApiProperty({
    example: "2025",
    description: "The year",
    required: false
  })
  @IsOptional()
  @IsString()
  year?: string;
}

export class TaskDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the task"
  })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({
    example: "Essay Writing Practice",
    description: "The title of the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Title is required" })
  title: string;

  @ApiProperty({
    example: "Write a short essay on your favorite book",
    description: "The description of the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Description is required" })
  description: string;

  @ApiProperty({
    example: 250,
    description: "The minimum word limit for the task"
  })
  @IsNumber()
  @IsNotEmpty({ message: "Minimum word limit is required" })
  wordLimitMinimum: number;

  @ApiProperty({
    example: 500,
    description: "The maximum word limit for the task",
    required: false
  })
  @IsNumber()
  @IsOptional()
  wordLimitMaximum?: number;

  @ApiProperty({
    example: true,
    description: "Whether the task is active",
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    example: 1,
    description: "The time period unit for the task",
    required: false
  })
  @IsNumber()
  @IsOptional()
  timePeriodUnit?: number;

  @ApiProperty({
    example: 1,
    description: "The deadline for the task (in days)",
    required: false
  })
  @IsNumber()
  @IsOptional()
  deadline?: number;

  @ApiProperty({
    example: "Follow the MLA format. Include citations and references.",
    description: "Instructions for completing the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Instructions are required" })
  instructions: string;

  @ApiProperty({
    type: TaskMetaDataDto,
    description: "Meta data for the task",
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TaskMetaDataDto)
  metaData?: TaskMetaDataDto;
}

export class TaskProgressDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the task"
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 75,
    description: "The progress percentage of the task"
  })
  @IsNumber()
  progress: number;
}

export class MissionResponseDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the mission"
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    example: "weekly",
    description: "The frequency of the mission",
    enum: MissionFrequency
  })
  @IsEnum(MissionFrequency)
  @IsNotEmpty()
  timeFrequency: MissionFrequency;

  @ApiProperty({
    example: 1,
    description: "The sequence number of the mission",
    default: 1,
    required: false
  })
  @IsNumber()
  @IsNotEmpty()
  sequenceNumber?: number;

  @ApiProperty({
    example: true,
    description: "Whether the mission is active",
    default: true
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    type: [TaskDto],
    description: "The tasks associated with this mission"
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskDto)
  tasks: TaskDto[];

  @ApiProperty({
    type: [TaskProgressDto],
    description: "The progress of each task in the mission"
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskProgressDto)
  @IsOptional()
  taskProgress?: {
    id: string;
    progress: number;
  }[];
}

export class CreateTaskDto {
  // @ApiProperty({
  //   example: "123e4567-e89b-12d3-a456-************",
  //   description: "The unique identifier of the task",
  //   required: false
  // })
  // @IsUUID()
  // @IsOptional()
  // id?: string;

  @ApiProperty({
    example: "Essay Writing Practice",
    description: "The title of the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Title is required" })
  title: string;

  @ApiProperty({
    example: "Write a short essay on your favorite book",
    description: "The description of the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Description is required" })
  description: string;

  @ApiProperty({
    example: 500,
    description: "The minimum word limit for the task"
  })
  @IsNumber()
  @Min(1, { message: "Word limit minimum must be at least 1" })
  @Type(() => Number)
  @IsNotEmpty({ message: "Word limit minimum is required" })
  wordLimitMinimum: number;

  @ApiProperty({
    example: 1000,
    description: "The maximum word limit for the task",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  wordLimitMaximum?: number;

  @ApiProperty({
    example: 1,
    description: "The time period unit for the task",
    default: 1,
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  timePeriodUnit?: number;

  @ApiProperty({
    example: 1,
    description: "The deadline for the task (in days)",
    required: false
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  deadline?: number;

  @ApiProperty({
    example: "Follow the MLA format. Include citations and references.",
    description: "Instructions for completing the task"
  })
  @IsString()
  @IsNotEmpty({ message: "Instructions are required" })
  instructions: string;

  @ApiProperty({
    example: true,
    description: "Whether the task is active",
    default: true,
    required: false
  })
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    type: TaskMetaDataDto,
    description: "Meta data for the task",
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TaskMetaDataDto)
  metaData?: TaskMetaDataDto;
}

export class CreateMissionDto {
  @ApiProperty({
    example: "weekly",
    description: "The frequency of the mission",
    enum: MissionFrequency
  })
  @IsEnum(MissionFrequency)
  @IsNotEmpty({ message: "Time frequency is required" })
  timeFrequency: MissionFrequency;

  @ApiProperty({
    description: "The tasks for this mission",
    type: [CreateTaskDto]
  })
  @IsArray()
  @ArrayMinSize(1, { message: "At least one task is required" })
  @ValidateNested({ each: true })
  @Type(() => CreateTaskDto)
  tasks: CreateTaskDto[];
}

class UpdateEssayMissionTaskDto extends PartialType(CreateTaskDto) {
  @ApiPropertyOptional({ description: 'ID of the task if updating an existing one' })
  @IsString()
  @IsOptional()
  id?: string;
}

class MissionPropertiesDto extends OmitType(CreateMissionDto, ['tasks'] as const) {}

class PartialMissionPropertiesDto extends PartialType(MissionPropertiesDto) {}


export class UpdateMissionDto extends PartialMissionPropertiesDto {
  @ApiPropertyOptional({
    type: [UpdateEssayMissionTaskDto],
    description: 'Tasks to be updated or added to the mission'
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateEssayMissionTaskDto)
  @IsOptional()
  tasks?: UpdateEssayMissionTaskDto[];
}

class SubmissionMetadataDto {
  @ApiProperty({
    example: "Mozilla/5.0...",
    description: "Browser information",
    required: false
  })
  @IsString()
  @IsOptional()
  browserInfo?: string;

  @ApiProperty({
    example: "***********",
    description: "IP Address",
    required: false
  })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiProperty({
    example: 1,
    description: "Number of submission attempts",
    required: false
  })
  @IsNumber()
  @IsOptional()
  submissionAttempts?: number;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Last draft saved timestamp",
    required: false
  })
  @IsDate()
  @IsOptional()
  lastDraftSavedAt?: Date;

  @ApiProperty({
    example: 300,
    description: "Time spent in seconds",
    required: false
  })
  @IsNumber()
  @IsOptional()
  timeSpent?: number;

  @ApiProperty({
    example: 50,
    description: "Word count difference from previous version",
    required: false
  })
  @IsNumber()
  @IsOptional()
  wordCountDiff?: number;

  @ApiProperty({
    description: "Content change analysis",
    required: false
  })
  @IsObject()
  @IsOptional()
  contentChanges?: {
    paragraphsAdded?: number;
    paragraphsRemoved?: number;
    significantChanges?: boolean;
  };

  @ApiProperty({
    example: "Check grammar in paragraph 2",
    description: "Reviewer notes",
    required: false
  })
  @IsString()
  @IsOptional()
  reviewerNotes?: string;

  @ApiProperty({
    example: 0.95,
    description: "AI content detection score",
    required: false
  })
  @IsNumber()
  @IsOptional()
  aiDetectionScore?: number;
}

export class EssayTaskSubmissionHistoryDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the submission history"
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: "This is the essay content...",
    description: "The content of the essay"
  })
  @IsString()
  content: string;

  @ApiProperty({
    example: 500,
    description: "Word count of the essay"
  })
  @IsNumber()
  @Min(0)
  wordCount: number;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Submission date"
  })
  @IsDate()
  submissionDate: Date;

  @ApiProperty({
    example: 1,
    description: "Sequence number of the submission"
  })
  @IsNumber()
  @Min(1)
  sequenceNumber: number;

  @ApiProperty({
    example: "Good structure, needs work on transitions",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  feedback?: string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Review date",
    required: false
  })
  @IsDate()
  @IsOptional()
  reviewedAt?: Date;

  @ApiProperty({
    type: SubmissionMetadataDto,
    description: "Additional metadata about the submission"
  })
  @ValidateNested()
  @Type(() => SubmissionMetadataDto)
  @IsOptional()
  metaData?: SubmissionMetadataDto;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "ID of the previous revision",
    required: false
  })
  @IsUUID()
  @IsOptional()
  previousRevisionId?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "Updated by user ID",
    required: false
  })
  @IsNotEmpty({ message: "Updated by user ID is required" })
  updatedBy: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "Updated by ID",
    required: false
  })
  @IsUUID()
  @IsOptional()
  updatedById?: string;

  @ApiProperty({
    example: "Creator 1",
    description: "The created by name",
    required: false,
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The created by ID",
    required: false
  })
  @IsUUID()
  @IsOptional()
  createdById?: string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "The date when the submission was created"
  })
  @IsDate()
  @IsNotEmpty({ message: "Created date is required" })
  createdAt: Date;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "The date when the submission was last updated"
  })
  @IsDate()
  @IsNotEmpty({ message: "Updated date is required" })
  updatedAt: Date;
}

export class TaskReferenceDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the task"
  })
  @IsUUID()
  @IsNotEmpty({ message: "Task ID is required" })
  id: string;

  @ApiProperty({
    example: 250,
    description: "The minimum word limit for the task"
  })
  @IsNumber()
  @IsNotEmpty({ message: "Minimum word limit is required" })
  wordLimitMinimum: number;

  @ApiProperty({
    example: 500,
    description: "The maximum word limit for the task",
    required: false
  })
  @IsNumber()
  @IsOptional()
  wordLimitMaximum?: number;
}

export class EssayTaskSubmissionDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the submission"
  })
  @IsUUID()
  @IsNotEmpty({ message: "Submission ID is required" })
  id: string;

  @ApiProperty({
    example: "This is the essay title...",
    description: "The title of the essay"
  })
  title?: string;

  @ApiProperty({
    example: "draft",
    description: "The status of the submission",
    enum: SubmissionStatus
  })
  @IsEnum(SubmissionStatus)
  @IsNotEmpty({ message: "Submission status is required" })
  status: SubmissionStatus;

  @ApiProperty({
    example: 1,
    description: "Current Revision number of the submission"
  })
  @IsNumber()
  @IsNotEmpty({ message: "Revision number is required" })
  @Min(1, { message: "Revision number must be at least 1" })
  currentRevision: number;

  @ApiProperty({
    example: true,
    description: "Whether the submission is active",
    default: true,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    type: [EssayTaskSubmissionHistoryDto],
    description: 'History of all submission revisions for this essay',
    isArray: true,
    required: true
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EssayTaskSubmissionHistoryDto)
  submissionHistory: EssayTaskSubmissionHistoryDto[];

  @ApiProperty({
    description: "The task details",
    example: {
      id: "123e4567-e89b-12d3-a456-************",
      wordLimitMinimum: 250,
      wordLimitMaximum: 500
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => TaskReferenceDto)
  @IsOptional()
  task?: TaskReferenceDto;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "updated by user ID",
    required: false
  })
  @IsNotEmpty({ message: "updated by user ID is required" })
  updatedBy: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "updated by user ID",
    required: false
  })
  @IsNotEmpty({ message: "Updated by user ID is required" })
  updatedById?: string;

  @ApiProperty({
    example: "Creator 1",
    description: "The created by name",
    required: false,
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The created by ID",
    required: false
  })
  @IsUUID()
  @IsOptional()
  createdById?: string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "The date when the submission was created"
  })
  @IsDate()
  @IsNotEmpty({ message: "Created date is required" })
  createdAt: Date;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "The date when the submission was last updated"
  })
  @IsDate()
  @IsNotEmpty({ message: "Updated date is required" })
  updatedAt: Date;

  @ApiProperty({
    type: DiarySkinResponseDto,
    description: "The skin associated with the submission",
    required: false
  })
  @IsOptional()
  diarySkin?: DiarySkinResponseDto;
}

export class StartMissionTaskDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The task ID to start"
  })
  @IsUUID()
  @IsOptional()
  taskId?: string;
}

export class CreateMissionTaskSubmissionDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The task ID to submit for"
  })
  @IsUUID()
  @IsOptional()
  taskId?: string;

  @ApiProperty({
    example: "This is the essay content...",
    description: "The content of the essay"
  })
  @IsString()
  @IsNotEmpty({ message: "Content is required" })
  content: string;

  @ApiProperty({
    example: "This is the essay title...",
    description: "The title of the essay"
  })
  @IsString()
  @IsNotEmpty({ message: "Title is required" })
  title: string;

  @ApiProperty({
    type: SubmissionMetadataDto,
    description: "Additional metadata about the submission",
    required: false
  })
  @ValidateNested()
  @Type(() => SubmissionMetadataDto)
  @IsOptional()
  metaData?: SubmissionMetadataDto;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The skin ID",
    required: false
  })
  @IsUUID()
  @IsOptional()
  skinId?: string;
}

export class EssayTaskActiveDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The task ID to activate/deactivate",
    nullable: true
  })
  @IsUUID()
  taskId: string;

  @ApiProperty({
    example: true,
    description: "Whether the task is active or not",
    nullable: false
  })
  @IsBoolean()
  @IsNotEmpty({ message: "Active status is required" })
  isActive: boolean;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission ID",
    nullable: true
  })
  @IsUUID()
  submissionId: string;
}

export class EssayTaskSubmissionUpdate {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the skin"
  })
  @IsUUID()
  @IsOptional()
  skinId?: string;
  
  @ApiProperty({
    example: "This is the updated essay title...",
    description: "The updated title of the essay"
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The Submission ID to update"
  })
  @IsUUID()
  @IsNotEmpty({ message: "Task ID is required" })
  submissionId: string;

  @ApiProperty({
    example: "This is the updated essay content...",
    description: "The updated content of the essay"
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({
    type: SubmissionMetadataDto,
    description: "Additional metadata about the submission",
    required: false
  })
  @ValidateNested()
  @Type(() => SubmissionMetadataDto)
  @IsOptional()
  metaData?: SubmissionMetadataDto;
}

export class EssayTaskSubmissionHistoryUpdate {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The Submission ID to update"
  })
  @IsUUID()
  @IsNotEmpty({ message: "Submission ID is required" })
  submissionId: string;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;

  @ApiProperty({
    example: "10",
    description: "The total points for the task"
  })
  @IsNumber()
  @IsOptional()
  points?: number;
}


export class CreateEssayTaskSubmissionMarkingDto {
  @ApiProperty({
    example: "10",
    description: "The total points for the task"
  })
  @IsNumber()
  @IsOptional()
  points?: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission ID to mark"
  })
  @IsUUID()
  submissionId: string;
}

export class UpdateEssayTaskSubmissionMarkingDto {
  @ApiProperty({
    example: 10,
    description: "The total points for the task",
  })
  @IsNumber()
  @IsOptional()
  points?: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;
}

export class EssayTaskSubmissionMarkingDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the marking"
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 10,
    description: "The total points for the task"
  })
  @IsNumber()
  points: number;

  @ApiProperty({
    example: "This is the feedback for the submission",
    description: "Feedback on the submission",
    required: false
  })
  @IsString()
  @IsOptional()
  submissionFeedback?: string;

  @ApiProperty({
    example: "This is the task remarks",
    description: "Remarks on the task",
    required: false
  })
  @IsString()
  @IsOptional()
  taskRemarks?: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission ID to mark"
  })
  @IsUUID()
  submissionId: string;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The submission history ID to mark"
  })
  @IsUUID()
  submissionHistoryId: string;

  @IsOptional()
  submission?: EssayTaskSubmissionDto;

  @IsOptional()
  submissionHistory?: EssayTaskSubmissionHistoryDto;
}

export class EssayModuleSkinPreferenceDto {
  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The unique identifier of the skin"
  })
  @IsUUID()
  skinId: string;

  @ApiProperty({
    example: "module_default",
    description: "The scope type of the skin",
    enum: SkinScopeType,
    required: false
  })
  @IsEnum(SkinScopeType)
  scopeType?: SkinScopeType;

  @ApiProperty({
    example: "123e4567-e89b-12d3-a456-************",
    description: "The task ID associated with the skin",
    required: false
  })
  @IsUUID()
  @IsOptional()
  taskId?: string;

  @ApiProperty({
    example: true,
    description: "Whether the skin is active or not",
    default: true,
    required: false
  })
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: "The skin preference ID",
    required: false,
    type: DiarySkinResponseDto
  })
  skin?: DiarySkinResponseDto;
}

export class TaskSkinInfoResponseDto {
  @ApiProperty({
    description: 'Task-specific skin configuration, if available',
    type: EssayModuleSkinPreference,
    required: false,
    nullable: true
  })
  taskSpecificSkin: EssayModuleSkinPreference | null;

  @ApiProperty({
    description: 'Module default skin configuration',
    type: EssayModuleSkinPreference,
    required: false,
    nullable: true
  })
  moduleDefaultSkin: EssayModuleSkinPreference | null;
}

export class SetDefaultSkinDto {
  @IsUUID()
  @IsNotEmpty()
  skinId: string;
}

