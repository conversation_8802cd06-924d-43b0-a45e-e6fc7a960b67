import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserType } from '../../database/entities/user.entity';
import { NotificationType } from '../../database/entities/notification.entity';

/**
 * Types of deeplinks supported by the application
 * @enum {string}
 */
export enum DeeplinkType {
  /** User profile */
  PROFILE = 'profile',
  /** Diary entry */
  DIARY_ENTRY = 'diary_entry',
  /** Diary feedback */
  DIARY_FEEDBACK = 'diary_feedback',
  /** Shared diary entry */
  DIARY_SHARE = 'diary_share',
  /** Friend shared diary entry */
  DIARY_FRIEND_SHARE = 'diary_friend_share',
  /** Notification */
  NOTIFICATION = 'notification',
  /** Email verification */
  VERIFICATION = 'verification',
  /** Password reset */
  PASSWORD_RESET = 'password_reset',
  /** Tutor-student matching */
  TUTOR_MATCHING = 'tutor_matching',
  /** Essay submission */
  ESSAY = 'essay',
  /** Shop item */
  SHOP_ITEM = 'shop_item',
  /** Diary mission */
  DIARY_MISSION = 'diary_mission',
  /** Mission diary entry */
  MISSION_DIARY_ENTRY = 'mission_diary_entry',
  /** Q&A submission */
  QA_SUBMISSION = 'qa_submission',
  /** Q&A review */
  QA_REVIEW = 'qa_review',
  /** Essay submission */
  ESSAY_SUBMISSION = 'essay_submission',
  /** Essay review */
  ESSAY_REVIEW = 'essay_review'
}

/**
 * Options for generating deeplinks
 * @interface
 */
export interface DeeplinkOptions {
  /** ID of the entity (e.g., user ID, diary entry ID) */
  id?: string;
  /** User ID for profile links */
  userId?: string;
  /** User type for profile links */
  userType?: UserType | string;
  /** Token for verification and password reset links */
  token?: string;
  /** Additional query parameters */
  additionalParams?: Record<string, string>;
  /** Text to display in HTML links */
  linkText?: string;
  /** CSS class for HTML links */
  cssClass?: string;
  /** Whether to style the link as a button */
  buttonStyle?: boolean;
  /** Notification type for notification links */
  notificationType?: NotificationType;
  /** Related entity type for notification links */
  relatedEntityType?: string;
}

/**
 * Unified service for generating all types of links in the application
 * This service provides a centralized approach for generating web links,
 * mobile deep links, and HTML links for all features of the application.
 */
@Injectable()
export class DeeplinkService {
  private readonly logger = new Logger(DeeplinkService.name);
  private readonly frontendUrl: string;
  private readonly mobileAppUrl: string;

  constructor(private readonly configService: ConfigService) {
    // Initialize URLs from config with fallbacks
    this.frontendUrl = this.getBaseUrl();
    this.mobileAppUrl = this.getMobileAppUrl();

    this.logger.log(`Initialized DeeplinkService with frontendUrl: ${this.frontendUrl}, mobileAppUrl: ${this.mobileAppUrl}`);
  }

  /**
   * Get the base URL for the frontend
   * @returns The base URL for the frontend
   */
  private getBaseUrl(): string {
    let baseUrl = this.configService.get<string>('FRONTEND_URL') || 'http://103.209.40.213:3011';

    // Remove trailing slash if present
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    return baseUrl;
  }

  /**
   * Get the base URL for mobile app deep links
   * @returns The base URL for mobile app deep links
   */
  private getMobileAppUrl(): string {
    let mobileUrl = this.configService.get<string>('FRONTEND_URL') || 'hecapp://';

    // Ensure the URL ends with a slash for consistency
    if (!mobileUrl.endsWith('/')) {
      mobileUrl = `${mobileUrl}/`;
    }

    return mobileUrl;
  }

  /**
   * Generate a web link for any entity in the application
   * @param type The type of link to generate
   * @param options Options for generating the link
   * @returns A web URL
   */
  getWebLink(type: DeeplinkType, options: DeeplinkOptions = {}): string {
    const path = this.getPath(type, options);
    const queryParams = this.buildQueryParams(options.additionalParams);

    return `${this.frontendUrl}/${path}${queryParams}`;
  }

  /**
   * Generate a mobile deep link for any entity in the application
   * @param type The type of link to generate
   * @param options Options for generating the link
   * @returns A mobile deep link URL
   */
  getDeepLink(type: DeeplinkType, options: DeeplinkOptions = {}): string {
    const path = this.getPath(type, options);
    const queryParams = this.buildQueryParams(options.additionalParams);

    return `${this.mobileAppUrl}${path}${queryParams}`;
  }

  /**
   * Generate an HTML link element
   * @param type The type of link to generate
   * @param options Options for generating the link
   * @returns An HTML anchor tag
   */
  getLinkHtml(type: DeeplinkType, options: DeeplinkOptions = {}): string {
    const url = this.getWebLink(type, options);
    const linkText = options.linkText || this.getDefaultLinkText(type);
    const className = options.cssClass ? ` class="${options.cssClass}"` : '';

    if (options.buttonStyle) {
      return this.getButtonHtml(url, linkText);
    }

    return `<a href="${url}"${className}>${linkText}</a>`;
  }

  /**
   * Generate a button-styled HTML link
   * @param url The URL for the button
   * @param buttonText The text to display on the button
   * @returns An HTML anchor tag styled as a button
   */
  private getButtonHtml(url: string, buttonText: string): string {
    return `<a href="${url}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; margin: 10px 0;">${buttonText}</a>`;
  }

  /**
   * Get the default link text for a given link type
   * @param type The type of link
   * @returns Default text for the link
   */
  private getDefaultLinkText(type: DeeplinkType): string {
    switch (type) {
      case DeeplinkType.PROFILE:
        return 'View Profile';
      case DeeplinkType.DIARY_ENTRY:
        return 'View Diary Entry';
      case DeeplinkType.DIARY_FEEDBACK:
        return 'View Feedback';
      case DeeplinkType.DIARY_SHARE:
        return 'View Shared Diary';
      case DeeplinkType.DIARY_FRIEND_SHARE:
        return 'View Friend Shared Diary';
      case DeeplinkType.NOTIFICATION:
        return 'View Notification';
      case DeeplinkType.VERIFICATION:
        return 'Verify Email';
      case DeeplinkType.PASSWORD_RESET:
        return 'Reset Password';
      case DeeplinkType.TUTOR_MATCHING:
        return 'View Tutor Assignment';
      case DeeplinkType.ESSAY:
        return 'View Essay';
      case DeeplinkType.SHOP_ITEM:
        return 'View Item';
      case DeeplinkType.DIARY_MISSION:
        return 'View Mission';
      case DeeplinkType.MISSION_DIARY_ENTRY:
        return 'View Mission Entry';
      case DeeplinkType.QA_SUBMISSION:
        return 'View Q&A Submission';
      case DeeplinkType.QA_REVIEW:
        return 'View Q&A Review';
      case DeeplinkType.ESSAY_SUBMISSION:
        return 'View Essay Submission';
      case DeeplinkType.ESSAY_REVIEW:
        return 'View Essay Review';
      default:
        return 'View';
    }
  }

  /**
   * Get the path for a given link type and options
   * @param type The type of link
   * @param options Options for generating the link
   * @returns The path component of the URL
   */
  private getPath(type: DeeplinkType, options: DeeplinkOptions): string {
    switch (type) {
      case DeeplinkType.PROFILE:
        return this.getProfilePath(options.userType, options.userId);

      case DeeplinkType.DIARY_ENTRY:
        return `diary/entries/${options.id}`;

      case DeeplinkType.DIARY_FEEDBACK:
        return `diary/feedback/${options.id}`;

      case DeeplinkType.DIARY_SHARE:
        return `diary/shared/${options.token || options.id}`;

      case DeeplinkType.DIARY_FRIEND_SHARE:
        return `diary/shared/${options.token || options.id}`;

      case DeeplinkType.NOTIFICATION:
        return this.getNotificationPath(options);

      case DeeplinkType.VERIFICATION:
        return `verify-email`;

      case DeeplinkType.PASSWORD_RESET:
        return `reset-password`;

      case DeeplinkType.TUTOR_MATCHING:
        if (options.relatedEntityType === 'student_tutor_mapping') {
          return `tutor-matching/mapping/${options.id}`;
        }
        return `tutor-matching`;

      case DeeplinkType.ESSAY:
        return `essay/submissions/${options.id}`;

      case DeeplinkType.SHOP_ITEM:
        return `shop/items/${options.id}`;

      case DeeplinkType.DIARY_MISSION:
        if (options.id) {
          return `diary/missions/${options.id}`;
        }
        return `diary/missions`;

      case DeeplinkType.MISSION_DIARY_ENTRY:
        return `diary/mission-entries/${options.id}`;

      case DeeplinkType.QA_SUBMISSION:
        return `qa/submissions/${options.id}`;

      case DeeplinkType.QA_REVIEW:
        return `qa/reviews/${options.id}`;

      case DeeplinkType.ESSAY_SUBMISSION:
        return `essay/submissions/${options.id}`;

      case DeeplinkType.ESSAY_REVIEW:
        return `essay/reviews/${options.id}`;

      default:
        this.logger.warn(`Unknown link type: ${type}`);
        return '';
    }
  }

  /**
   * Get the profile path based on user type
   * @param userType The user type
   * @param userId The user ID
   * @returns The path to the user's profile
   */
  private getProfilePath(userType: UserType | string, userId: string): string {
    if (!userId) {
      this.logger.warn('getProfilePath called with empty userId');
      return '';
    }

    switch (userType) {
      case UserType.ADMIN:
        return `profiles/admin/${userId}`;
      case UserType.TUTOR:
        return `tutors/profile/${userId}`;
      case UserType.STUDENT:
        return `dashboard/students/profile/${userId}`;
      default:
        return `dashboard/students/profile/${userId}`;
    }
  }

  /**
   * Get the notification path based on notification type
   * @param options Options containing notification details
   * @returns The path for the notification
   */
  private getNotificationPath(options: DeeplinkOptions): string {
    if (!options.notificationType) {
      return `notifications/${options.id || ''}`;
    }

    switch (options.notificationType) {
      case NotificationType.DIARY_SUBMISSION:
        return `dashboard/submission-management/hec-diary/review/${options.id}`;

      case NotificationType.DIARY_REVIEW:
        return `diary?entryId=${options.id}`;

      case NotificationType.DIARY_FEEDBACK:
        return `diary?entryId=${options.id}`;

      case NotificationType.TUTOR_ASSIGNMENT:
        if (options.relatedEntityType === 'student_tutor_mapping') {
          return `tutor-matching/mapping/${options.id}`;
        }
        return `tutor-matching`;

      case NotificationType.TUTOR_VERIFICATION:
        if (options.relatedEntityType === 'tutor') {
          return `profiles/tutor/${options.id}`;
        }
        return `tutor-approval`;

      case NotificationType.MISSION_CREATED:
        return `diary-missions/submission/${options.id}`;

      case NotificationType.MISSION_SUBMISSION:
        return `dashboard/submission-management/hec-diary/review/${options.id}`;

      case NotificationType.MISSION_FEEDBACK:
        return `diary-missions/submission/${options.id}`;

      case NotificationType.MISSION_CORRECTION:
        return `diary-missions/submission/${options.id}`;

      case NotificationType.MISSION_REVIEW_COMPLETE:
        return `diary-missions/submission/${options.id}`;

      case NotificationType.QA_SUBMISSION:
        return `qa/submissions/${options.id}`;

      case NotificationType.QA_REVIEW:
        return `qa/reviews/${options.id}`;
      case NotificationType.QA_FEEDBACK:
        return `qa/reviews/${options.id}`;

      case NotificationType.ESSAY_SUBMISSION:
        return `essay/submissions/${options.id}`;

      case NotificationType.ESSAY_REVIEW:
        return `qa/reviews/${options.id}`;

      case NotificationType.ESSAY_FEEDBACK:
        return `essay/reviews/${options.id}`;

      default:
        return `notifications/${options.id || ''}`;
    }
  }

  /**
   * Build query parameters string from a record
   * @param params Record of parameter key-value pairs
   * @returns Query string including the leading '?' if params exist
   */
  private buildQueryParams(params?: Record<string, string>): string {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });

    return `?${queryParams.toString()}`;
  }

  // Backward compatibility methods

  /**
   * Generate a profile link for a user (backward compatibility)
   * @param userId The user ID
   * @param userType The user type
   * @returns A URL to the user's profile
   */
  getProfileLink(userId: string, userType: UserType | string): string {
    return this.getWebLink(DeeplinkType.PROFILE, { userId, userType });
  }

  /**
   * Generate a deep link for a user profile (backward compatibility)
   * @param userId The user ID
   * @param userType The user type
   * @returns A deep link URL to the user's profile
   */
  getProfileDeepLink(userId: string, userType: UserType | string): string {
    return this.getDeepLink(DeeplinkType.PROFILE, { userId, userType });
  }

  /**
   * Generate an HTML link for a user profile (backward compatibility)
   * @param userId The user ID
   * @param userType The user type
   * @param linkText The text to display in the link
   * @param cssClass Optional CSS class
   * @returns An HTML anchor tag
   */
  getProfileLinkHtml(
    userId: string,
    userType: UserType | string,
    linkText: string = 'View Profile',
    cssClass?: string
  ): string {
    return this.getLinkHtml(DeeplinkType.PROFILE, {
      userId,
      userType,
      linkText,
      cssClass
    });
  }

  /**
   * Generate a button-styled HTML link for a user profile (backward compatibility)
   * @param userId The user ID
   * @param userType The user type
   * @param buttonText The text to display on the button
   * @returns An HTML anchor tag styled as a button
   */
  getProfileButtonHtml(
    userId: string,
    userType: UserType | string,
    buttonText: string = 'View Profile'
  ): string {
    return this.getLinkHtml(DeeplinkType.PROFILE, {
      userId,
      userType,
      linkText: buttonText,
      buttonStyle: true
    });
  }
}
