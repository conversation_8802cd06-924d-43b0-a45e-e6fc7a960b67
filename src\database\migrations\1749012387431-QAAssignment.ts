import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignment1749012387431 implements MigrationInterface {
    name = 'QAAssignment1749012387431'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa-assignment-sets" ADD "score" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE "qa-assignment-sets" DROP COLUMN "score"`);
    }
}
