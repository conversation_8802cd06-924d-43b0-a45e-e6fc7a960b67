import { HttpException, HttpStatus, Injectable, UnauthorizedException, ConflictException, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { getCurrentUTCDate, addMinutesUTC } from '../../common/utils/date-utils';
import { trimUserId } from '../../common/utils/user-utils';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as crypto from 'crypto'; // Used for generating tokens
import { CreateRoleDto, ForgotPasswordDto, ForgotUserIdDto, LoginUserDto, RegisterDto, ResendVerificationEmailDto, ResetPasswordDto, UserResponseDto, ChangePasswordDto } from '../../database/models/users.dto';
import { UserType } from '../../database/entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { Role } from '../../database/entities/role.entity';
import { UserRole } from '../../database/entities/user-role.entity';
import { DataSource, Repository } from 'typeorm';
import { User } from '../../database/entities/user.entity';
import { PasswordReset } from '../../database/entities/password-reset.entity';
import { EmailVerification } from '../../database/entities/email-verification.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import { Diary } from '../../database/entities/diary.entity';
// Plan import removed as it's not used
import { JwtPayload } from './interfaces/jwt-payload.interface';
import EmailService from '../../common/services/email.service';
import { DiaryService } from '../diary/diary.service';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { Request } from 'express';

@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);

    constructor(
        private readonly userService: UsersService,
        private readonly mailService: EmailService,
        private readonly jwtService: JwtService,
        private readonly profilePictureService: ProfilePictureService,
        private readonly dataSource: DataSource,
        @Inject(forwardRef(() => DiaryService))
        private readonly diaryService: DiaryService,
        private readonly notificationHelper: NotificationHelperService,
        private readonly deeplinkService: DeeplinkService,
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @InjectRepository(UserPlan)
        private readonly userPlanRepository: Repository<UserPlan>,
        @InjectRepository(PasswordReset)
        private readonly passwordResetRepository: Repository<PasswordReset>,
        @InjectRepository(EmailVerification)
        private readonly emailVerificationRepository: Repository<EmailVerification>,
        @InjectRepository(TutorApproval)
        private readonly tutorApprovalRepository: Repository<TutorApproval>,
        @InjectRepository(Diary)
        private readonly diaryRepository: Repository<Diary>,
    ) {}

    async validateUser(userId: string, pass: string): Promise<any> {
        const user = await this.userService.findByUserId(userId);
        if (!user) {
            return null;
        }

        try {
            if (!user.verifyPassword(pass)) {
                return null;
            }
        } catch (error) {
            this.logger.error(`Error verifying password: ${error.message}`);
            return null;
        }

        const { password, ...result } = user;
        return result;
    }

    async getRoles() {
        return this.roleRepository.find();
    }

    /**
     * Check if a tutor is approved
     * @param userId The user ID to check
     * @returns Object containing approval status and details
     */
    async checkTutorApprovalStatus(userId: string): Promise<{ isApproved: boolean; status?: TutorApprovalStatus; message?: string }> {
        // Find the tutor approval record
        const tutorApproval = await this.tutorApprovalRepository.findOne({
            where: { userId: userId },
            order: { createdAt: 'DESC' } // Get the most recent approval record
        });

        // If no approval record exists, the tutor is not approved
        if (!tutorApproval) {
            return {
                isApproved: false,
                status: TutorApprovalStatus.PENDING,
                message: 'Your tutor account is pending approval. Please wait for an administrator to approve your account.'
            };
        }

        // Check the approval status
        if (tutorApproval.status === TutorApprovalStatus.APPROVED) {
            return { isApproved: true, status: TutorApprovalStatus.APPROVED };
        } else if (tutorApproval.status === TutorApprovalStatus.REJECTED) {
            return {
                isApproved: false,
                status: TutorApprovalStatus.REJECTED,
                message: `Your tutor account has been rejected. Reason: ${tutorApproval.rejectionReason || 'No reason provided'}. Please contact an administrator for more information.`
            };
        } else {
            return {
                isApproved: false,
                status: TutorApprovalStatus.PENDING,
                message: 'Your tutor account is pending approval. Please wait for an administrator to approve your account.'
            };
        }
    }

    async login(loginDto: LoginUserDto) {
        try {
            // Trim the userId to remove any whitespace
            const trimmedUserId = trimUserId(loginDto.userId);

            // Find the user by trimmed userId
            const user = await this.userService.findByUserId(trimmedUserId);
            if (!user) {
                throw new UnauthorizedException('Invalid user ID');
            }

            // Validate password using the entity's method
            const isPasswordValid = user.verifyPassword(loginDto.password);

            if (!isPasswordValid) {
                this.logger.warn(`Invalid password attempt for user: ${loginDto.userId}`);
                throw new UnauthorizedException('Invalid password');
            }

            // Check if user is confirmed
            if (!user.isConfirmed) {
                throw new UnauthorizedException('Account not confirmed. Please verify your email first.');
            }

            // Check if user is active
            if (!user.isActive) {
                throw new UnauthorizedException('Your account is inactive. Please contact an administrator.');
            }

            // For tutors, check approval status
            if (user.type === UserType.TUTOR) {
                const approvalStatus = await this.checkTutorApprovalStatus(user.id);

                if (!approvalStatus.isApproved) {
                    throw new UnauthorizedException(approvalStatus.message);
                }
            }

            // If no role is selected, determine the highest role the user has
            let selectedRole = loginDto.selectedRole;
            if (!selectedRole) {
                // Get all roles the user has
                const userRoleNames = user.userRoles ? user.userRoles.map(ur => ur.role.name) : [];

                // Check for admin role first (highest priority)
                if (user.type === UserType.ADMIN || userRoleNames.includes('admin')) {
                    selectedRole = UserType.ADMIN;
                }
                // Then check for tutor role
                else if (user.type === UserType.TUTOR || userRoleNames.includes('tutor')) {
                    selectedRole = UserType.TUTOR;
                }
                // Default to student role
                else {
                    selectedRole = UserType.STUDENT;
                }
            }
            // If role is selected, validate it against user's roles
            else if (user.type !== selectedRole) {
                // Check if user has the selected role in their roles
                const hasRole = user.userRoles &&
                    user.userRoles.some(userRole => {
                        console.log('Validating role:', userRole.role.name, 'against', selectedRole);
                        if (selectedRole === UserType.ADMIN) {
                            return userRole.role.name === 'admin';
                        } else if (selectedRole === UserType.TUTOR) {
                            return userRole.role.name === 'tutor';
                        } else if (selectedRole === UserType.STUDENT) {
                            return userRole.role.name === 'student';
                        }
                        return false;
                    });

                if (!hasRole) {
                    throw new UnauthorizedException(`You do not have ${selectedRole} access. Please select the correct role.`);
                }
            }

            // Update last login time
            user.lastLoginAt = new Date();

            // Handle remember me functionality
            if (loginDto.rememberMe) {
                // Generate refresh token
                const refreshToken = crypto.randomBytes(40).toString('hex');

                // Set refresh token expiry (30 days)
                const refreshTokenExpiry = new Date();
                refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30);

                // Save refresh token to user
                user.refreshToken = refreshToken;
                user.refreshTokenExpiry = refreshTokenExpiry;
            } else {
                // Clear any existing refresh token by setting to empty string/date
                user.refreshToken = '';
                user.refreshTokenExpiry = new Date(0); // January 1, 1970
            }

            await this.userRepository.save(user);

            // Get active subscription plan for student users
            let activePlan: string | undefined = undefined;
            let planInfo: Record<string, any> = {};

            if (selectedRole === UserType.STUDENT) {
                try {
                    // Find active user plan with plan details
                    const userPlan = await this.userPlanRepository.findOne({
                        where: { userId: user.id, isActive: true },
                        relations: ['plan']
                    });

                    if (userPlan && userPlan.plan) {
                        activePlan = userPlan.plan.name;
                        // Check if plan has expired
                        const now = new Date();
                        const isActive = userPlan.endDate > now;

                        planInfo = {
                            planId: userPlan.planId,
                            planType: userPlan.plan.type,
                            planExpiryDate: userPlan.endDate.toISOString(),
                            planActive: isActive
                        };

                        // Log if plan has expired
                        if (!isActive) {
                            this.logger.warn(`User ${user.id} has an expired subscription plan: ${activePlan}`);
                        }
                    }
                } catch (err) {
                    this.logger.error(`Error fetching subscription plan for user ${user.id}: ${err.message}`);
                    // Continue without plan info
                }
            }

            // Get user roles
            const userRoles = user.userRoles ? user.userRoles.map(userRole => userRole.role.name) : [];
            this.logger.log(`User ${user.name} (${user.id}) has roles: ${JSON.stringify(userRoles)}`);

            // Get default skin ID for student users
            let defaultSkinId = null;
            if (selectedRole === UserType.STUDENT) {
                try {
                    // Start a transaction to ensure we get the latest data
                    const queryRunner = this.dataSource.createQueryRunner();
                    await queryRunner.connect();
                    await queryRunner.startTransaction();

                    try {
                        // Use a direct SQL query with FOR UPDATE to get the latest defaultSkinId and lock the row
                        const diaryResult = await queryRunner.query(
                            `SELECT default_skin_id FROM diary WHERE user_id = $1 FOR UPDATE LIMIT 1`,
                            [user.id]
                        );

                        if (diaryResult && diaryResult.length > 0) {
                            defaultSkinId = diaryResult[0].default_skin_id;
                            this.logger.log(`Found default skin ID ${defaultSkinId} for user ${user.id} using direct SQL query with transaction`);
                        } else {
                            this.logger.log(`No diary found for user ${user.id}, will create one on first diary access`);
                        }

                        // Commit the transaction
                        await queryRunner.commitTransaction();
                    } catch (transactionError) {
                        // Rollback the transaction in case of error
                        await queryRunner.rollbackTransaction();
                        throw transactionError;
                    } finally {
                        // Release the query runner
                        await queryRunner.release();
                    }
                } catch (err) {
                    this.logger.error(`Error fetching diary for user ${user.id}: ${err.message}`);

                    // Fallback to a simple query if the transaction approach fails
                    try {
                        const fallbackResult = await this.dataSource.query(
                            `SELECT default_skin_id FROM diary WHERE user_id = $1 LIMIT 1`,
                            [user.id]
                        );

                        if (fallbackResult && fallbackResult.length > 0) {
                            defaultSkinId = fallbackResult[0].default_skin_id;
                            this.logger.log(`Found default skin ID ${defaultSkinId} for user ${user.id} using fallback query`);
                        }
                    } catch (fallbackErr) {
                        this.logger.error(`Fallback query also failed for user ${user.id}: ${fallbackErr.message}`);
                    }
                }
            }            // Generate JWT token with selected role information and plan details
            const payload: JwtPayload = {
                id: user.id,
                username: user.userId, // Use userId instead of email
                sub: user.id,
                name: user.name,
                type: user.type,
                selectedRole: selectedRole, // Use the determined or validated role
                roles: userRoles,
                defaultSkinId: defaultSkinId,
                activePlan: activePlan,
                ...planInfo
            };

            this.logger.log(`Generated JWT payload for user ${user.name}: ${JSON.stringify(payload, null, 2)}`);

            // Set token expiration based on remember me
            const tokenExpiresIn = loginDto.rememberMe ? '30d' : '1d';

            // Generate token and decode to get actual expiry time
            const token = this.jwtService.sign(payload, { expiresIn: tokenExpiresIn });
            const decoded = this.jwtService.decode(token) as { exp: number };
            const tokenExpiryDate = new Date(decoded.exp * 1000); // Convert exp to milliseconds

            // Get user DTO
            const userDto = user.toDto(selectedRole);
            userDto.defaultSkinId = defaultSkinId; // Add default skin ID to user DTO
            // Add profile picture URL if the user has a profile picture
            const hasProfilePicture = await this.profilePictureService.hasProfilePicture(user.id);
            if (hasProfilePicture) {
                // Use a direct URL for the profile picture
                userDto.profilePictureUrl = await this.profilePictureService.getProfilePictureDirectUrl(user.id);
            } else {
                userDto.profilePictureUrl = null;
            }            const response = {
                access_token: token, // Use the already generated token
                token_expires: tokenExpiryDate.toISOString(),
                user: userDto,
                returnUrl: loginDto.returnUrl || '/home'
            };

            // Add refresh token to response if remember me is enabled
            if (loginDto.rememberMe) {
                response['refresh_token'] = user.refreshToken;
                response['refresh_token_expires'] = user.refreshTokenExpiry;
            }

            return response;
        } catch (error) {
            // For authentication-specific exceptions, pass them through with detailed messages
            if (error instanceof UnauthorizedException) {
                this.logger.warn(`Authentication failed for user ${loginDto.userId}: ${error.message}`);
                throw error;
            }

            // For validation errors, provide specific feedback
            if (error.name === 'ValidationError' || error.name === 'BadRequestException') {
                this.logger.warn(`Validation error during login for user ${loginDto.userId}: ${error.message}`);
                throw new BadRequestException({
                    message: 'Login validation failed',
                    error: error.response?.message || error.message,
                    details: error.response?.error || null
                });
            }

            // For database-related errors, log details but return a generic message
            if (error.code && (error.code.startsWith('23') || error.code.startsWith('22'))) { // PostgreSQL error codes
                this.logger.error(`Database error during login for user ${loginDto.userId}: ${error.message}`, error.stack);
                throw new HttpException(
                    'Database error occurred during login. Please try again later.',
                    HttpStatus.INTERNAL_SERVER_ERROR
                );
            }

            // For all other errors, log detailed information but return a generic message
            const errorId = `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase();
            this.logger.error(
                `[ERROR-REF:${errorId}] Login failed for user ${loginDto.userId}: ${error.message}`,
                error.stack,
                JSON.stringify({ userId: loginDto.userId, errorType: error.name || 'Unknown' })
            );

            throw new HttpException(
                {
                    message: 'An error occurred during login. Please try again.',
                    error: 'Login process failed',
                    errorRefId: errorId
                },
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async verifyEmail(token: string) {
        try {
            // Find the email verification record by token
            const emailVerification = await this.emailVerificationRepository.findOne({
                where: {
                    token: token,
                    isUsed: false
                }
            });

            if (!emailVerification) {
                throw new BadRequestException('Invalid or expired verification token');
            }

            // Check if token is expired
            const currentTime = new Date().getTime();
            const expirationTime = new Date(emailVerification.expirationTime).getTime();

            if (currentTime > expirationTime) {
                throw new BadRequestException('Verification link has been expired');
            }

            // Find the user
            const user = await this.userRepository.findOne({
                where: { id: emailVerification.userId }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            // Update user status
            user.isConfirmed = true;
            await this.userRepository.save(user);

            // If user is a tutor, create a tutor approval request and notify admins
            if (user.type === UserType.TUTOR) {
                const tutorApproval = new TutorApproval();
                tutorApproval.userId = user.id;
                tutorApproval.status = TutorApprovalStatus.PENDING;

                await this.tutorApprovalRepository.save(tutorApproval);

                // Send notifications to all admin users
                await this.notifyAdminsAboutTutorVerification(user);
            }

            // Mark the token as used
            emailVerification.isUsed = true;
            await this.emailVerificationRepository.save(emailVerification);

            // Generate JWT token
            const payload: JwtPayload = {
                id: user.id,
                username: user.userId, // Use userId instead of email
                sub: user.id,
                name: user.name,
                type: user.type,
                roles: user.userRoles ? user.userRoles.map(userRole => userRole.role.name) : []
            };

            // Prepare response message based on user type
            let message = 'Email verified successfully';
            if (user.type === UserType.TUTOR) {
                message = 'Email verified successfully. Your tutor account is pending admin approval.';
            }

            // For tutors, include approval status
            let approvalStatus = null;
            if (user.type === UserType.TUTOR) {
                approvalStatus = {
                    status: TutorApprovalStatus.PENDING,
                    message: 'Your tutor account is pending admin approval.'
                };
            }

            // Get user DTO
            const userDto = user.toDto();

            // Add profile picture URL if the user has a profile picture
            const hasProfilePicture = await this.profilePictureService.hasProfilePicture(user.id);
            if (hasProfilePicture) {
                // Use a direct URL for the profile picture
                userDto.profilePictureUrl = await this.profilePictureService.getProfilePictureDirectUrl(user.id);
            } else {
                userDto.profilePictureUrl = null;
            }

            return {
                success: true,
                message,
                access_token: this.jwtService.sign(payload),
                user: userDto,
                requiresApproval: user.type === UserType.TUTOR,
                approvalStatus: approvalStatus
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            throw new HttpException(
                error.message || 'Email verification failed',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async register(registerDto: RegisterDto) {
        this.logger.log(`Registration attempt for user: ${registerDto.userId}, email: ${registerDto.email}`);

        // Validate registration data before starting transaction
        await this.validateRegistrationData(registerDto);

        // Start a transaction
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();

        // Variable to track if transaction was started
        let transactionStarted = false;

        try {
            // Start transaction
            await queryRunner.startTransaction();
            transactionStarted = true;
            this.logger.log('Transaction started for registration');

            // Create the user within transaction
            const user = await this.createUserWithTransaction(registerDto, queryRunner);

            // Generate and save verification token within transaction
            const token = await this.createVerificationTokenWithTransaction(user.id, queryRunner);

            // Commit the transaction
            await queryRunner.commitTransaction();
            transactionStarted = false;
            this.logger.log('Transaction committed successfully for registration');

            // Send verification email (non-blocking, after transaction)
            this.sendVerificationEmail(user.email, token).catch(error => {
                this.logger.error(`Failed to send verification email: ${error.message}`, error.stack);
            });

            this.logger.log(`Registration successful for user: ${user.userId}, ID: ${user.id}`);

            return {
                success: true,
                message: 'Registration successful. Please check your email for verification link.',
                userId: user.id
            };
        } catch (error) {
            this.logger.error(`Registration failed: ${error.message}`, error.stack);

            // Rollback transaction if it was started
            if (transactionStarted) {
                try {
                    this.logger.log('Rolling back transaction due to error');
                    await queryRunner.rollbackTransaction();
                    this.logger.log('Transaction rolled back successfully');
                } catch (rollbackError) {
                    this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
                }
            }

            // Handle specific error types
            if (error instanceof BadRequestException || error instanceof ConflictException) {
                throw error;
            }

            // Handle database errors
            if (error.code === '23505') {
                return this.handleUniqueConstraintViolation(error);
            }

            // Generic error
            const errorId = `REG-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase();
            this.logger.error(
                `[ERROR-REF:${errorId}] Registration failed: ${error.message}`,
                error.stack,
                JSON.stringify({ email: registerDto.email, userId: registerDto.userId })
            );

            throw new HttpException(
                {
                    message: 'Registration failed. Please try again later.',
                    error: {
                        type: 'ServerError',
                        refId: errorId
                    }
                },
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        } finally {
            // Always release the query runner
            if (queryRunner) {
                await queryRunner.release();
                this.logger.log('Query runner released');
            }
        }
    }

    /**
     * Validate registration data
     */
    private async validateRegistrationData(registerDto: RegisterDto): Promise<void> {
        const validationErrors: Record<string, string[]> = {};

        // Trim the userId
        const trimmedUserId = trimUserId(registerDto.userId);
        registerDto.userId = trimmedUserId;

        // Check if user already exists by email
        const existingUser = await this.userService.findByEmail(registerDto.email);
        if (existingUser) {
            validationErrors['email'] = [`Email ${registerDto.email} is already registered`];
            throw new ConflictException({
                success: false,
                message: 'Registration failed',
                validationErrors: validationErrors,
                statusCode: HttpStatus.CONFLICT
            });
        }

        // Check if userId already exists
        const existingUserId = await this.userService.findByUserId(trimmedUserId);
        if (existingUserId) {
            validationErrors['userId'] = [`User ID ${trimmedUserId} is already taken`];
            throw new ConflictException({
                success: false,
                message: 'Registration failed',
                validationErrors: validationErrors,
                statusCode: HttpStatus.CONFLICT
            });
        }

        // Only allow student and tutor registration
        if (registerDto.type && ![UserType.STUDENT, UserType.TUTOR].includes(registerDto.type)) {
            validationErrors['type'] = ['Only student and tutor registration is allowed through this endpoint'];
        }

        // Check if passwords match
        if (registerDto.password !== registerDto.confirmPassword) {
            validationErrors['confirmPassword'] = ['Passwords do not match'];
        }

        // Check if user agreed to terms and conditions
        if (!registerDto.agreedToTerms) {
            validationErrors['agreedToTerms'] = ['You must agree to the terms and conditions'];
        }

        // If there are validation errors, throw a BadRequestException
        if (Object.keys(validationErrors).length > 0) {
            throw new BadRequestException({
                message: 'Registration validation failed',
                error: {
                    type: 'ValidationError',
                    details: validationErrors
                }
            });
        }
    }

    /**
     * Create user with transaction
     */
    private async createUserWithTransaction(registerDto: RegisterDto, queryRunner: any): Promise<User> {
        this.logger.log(`Creating user with transaction: ${registerDto.userId}, type: ${registerDto.type}`);

        // Create user based on type
        const trimmedUserId = trimUserId(registerDto.userId);

        // Create the user entity
        const newUser = new User();
        newUser.name = trimmedUserId;
        newUser.userId = trimmedUserId;
        newUser.email = registerDto.email;
        newUser.phoneNumber = registerDto.phoneNumber;
        newUser.gender = registerDto.gender;
        newUser.bio = registerDto.bio;
        newUser.agreedToTerms = registerDto.agreedToTerms || false;
        newUser.type = registerDto.type;
        newUser.isConfirmed = false;
        newUser.isActive = registerDto.type === UserType.STUDENT; // Students active, tutors inactive

        // Set password
        newUser.setPassword(registerDto.password);

        // Save the user using the transaction
        const userRepository = queryRunner.manager.getRepository(User);
        const savedUser = await userRepository.save(newUser);
        this.logger.log(`User created with ID: ${savedUser.id}`);

        // Find or create role
        const roleRepository = queryRunner.manager.getRepository(Role);
        const roleName = registerDto.type === UserType.STUDENT ? 'student' : 'tutor';
        let role = await roleRepository.findOne({ where: { name: roleName } });

        if (!role) {
            role = roleRepository.create({ name: roleName });
            role = await roleRepository.save(role);
            this.logger.log(`Created new role: ${roleName}`);
        }

        // Create user role
        const userRoleRepository = queryRunner.manager.getRepository(UserRole);
        const userRole = new UserRole();
        userRole.userId = savedUser.id;
        userRole.roleId = role.id;

        await userRoleRepository.save(userRole);
        this.logger.log(`Assigned role ${roleName} to user ${savedUser.id}`);

        return savedUser;
    }

    /**
     * Create verification token for user with transaction
     */
    private async createVerificationTokenWithTransaction(userId: string, queryRunner: any): Promise<string> {
        this.logger.log(`Creating verification token with transaction for user ${userId}`);

        // Generate a secure random token
        const token = crypto.randomBytes(32).toString('hex');

        // Get repository from transaction
        const emailVerificationRepository = queryRunner.manager.getRepository(EmailVerification);

        // Delete any existing tokens for this user
        await emailVerificationRepository.delete({ userId });

        // Create new verification token
        const emailVerification = new EmailVerification();
        emailVerification.userId = userId;
        emailVerification.token = token;
        emailVerification.expirationTime = addMinutesUTC(getCurrentUTCDate(), 5);
        emailVerification.isUsed = false;

        await emailVerificationRepository.save(emailVerification);
        this.logger.log(`Created verification token for user ${userId}`);

        return token;
    }

    /**
     * Send verification email
     */
    private async sendVerificationEmail(email: string, token: string): Promise<void> {
        try {
            const emailSent = await this.mailService.sendVerificationLink(email, token);

            if (emailSent) {
                this.logger.log(`Verification email sent to ${email}`);
            } else {
                this.logger.warn(`Failed to send verification email to ${email}`);
            }
        } catch (error) {
            this.logger.error(`Error sending verification email to ${email}: ${error.message}`, error.stack);
            // Don't rethrow - this is a non-critical operation
        }
    }

    /**
     * Handle unique constraint violation
     */
    private handleUniqueConstraintViolation(error: any): never {
        const field = error.detail.includes('email') ? 'email' :
                     error.detail.includes('userId') ? 'userId' : 'unknown';

        const validationErrors: Record<string, string[]> = {};
        validationErrors[field] = [`This ${field} is already in use`];

        this.logger.warn(`Registration database constraint error: ${error.detail}`);

        throw new ConflictException({
            success: false,
            message: 'Registration failed',
            validationErrors: validationErrors,
            statusCode: HttpStatus.CONFLICT
        });
    }

    async createRole(createRoleDto: CreateRoleDto) {
        try {
            // Check if role already exists
            const existingRole = await this.roleRepository.findOne({ where: { name: createRoleDto.name } });
            if (existingRole) {
                const validationErrors: Record<string, string[]> = {};
                validationErrors['name'] = [`Role ${createRoleDto.name} already exists`];

                throw new ConflictException({
                    success: false,
                    message: 'Role creation failed',
                    validationErrors: validationErrors,
                    statusCode: HttpStatus.CONFLICT
                });
            }

            // Create role
            const role = this.roleRepository.create({
                ...createRoleDto

            });
            return await this.roleRepository.save(role);
        } catch (error) {
            if (error instanceof ConflictException) {
                throw error;
            }
            throw new HttpException(
                error.message || 'Role creation failed',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
        try {
            // Check if the identifier is an email or userId
            const isEmail = forgotPasswordDto.identifier.includes('@');

            // Find user by email or userId based on the format of the identifier
            let user: User | null = null;
            if (isEmail) {
                user = await this.userRepository.findOne({ where: { email: forgotPasswordDto.identifier } });
            } else {
                user = await this.userRepository.findOne({ where: { userId: forgotPasswordDto.identifier } });
            }

            if (!user) {
                // For security reasons, don't reveal that the user doesn't exist
                return {
                    success: true,
                    message: 'If your email is registered, you will receive a password reset link.'
                };
            }

            // Generate a secure random token
            const token = crypto.randomBytes(32).toString('hex');

            // Delete any existing password reset tokens for this user
            await this.passwordResetRepository.delete({ userId: user.id });

            // Save the new password reset token
            const passwordReset = new PasswordReset();
            passwordReset.userId = user.id;
            passwordReset.token = token;
            passwordReset.expirationTime = addMinutesUTC(getCurrentUTCDate(), 5); // 5 minutes expiration
            passwordReset.isUsed = false;


            await this.passwordResetRepository.save(passwordReset);

            // Send password reset link to the user's email
            const emailSent = await this.mailService.sendPasswordResetLink(user.email, token);

            if (!emailSent) {
                throw new HttpException(
                    'Failed to send password reset email. Please try again.',
                    HttpStatus.INTERNAL_SERVER_ERROR
                );
            }

            return {
                success: true,
                message: 'Password reset OTP has been sent to your email.'
            };
        } catch (error) {
            throw new HttpException(
                'Failed to process request',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async resetPassword(resetPasswordDto: ResetPasswordDto) {
        try {
            // Find the password reset record by token
            const passwordReset = await this.passwordResetRepository.findOne({
                where: {
                    token: resetPasswordDto.token,
                    isUsed: false
                }
            });

            if (!passwordReset) {
                throw new BadRequestException('Invalid or expired password reset token');
            }

            // Check if token is expired
            const currentTime = new Date().getTime();
            const expirationTime = new Date(passwordReset.expirationTime).getTime();

            if (currentTime > expirationTime) {
                throw new BadRequestException('Password reset link has expired');
            }

            // Find the user
            const user = await this.userRepository.findOne({
                where: { id: passwordReset.userId }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            // Update password
            user.setPassword(resetPasswordDto.newPassword);
            await this.userRepository.save(user);

            // Mark the token as used
            passwordReset.isUsed = true;
            await this.passwordResetRepository.save(passwordReset);

            return {
                success: true,
                message: 'Password has been reset successfully. You can now login with your new password.'
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            throw new HttpException(
                error.message || 'Password reset failed',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async forgotUserId(forgotUserIdDto: ForgotUserIdDto) {
        try {
            // Find user by email
            const user = await this.userRepository.findOne({ where: { email: forgotUserIdDto.email } });
            if (!user) {
                // For security reasons, don't reveal that the user doesn't exist
                return {
                    success: true,
                    message: 'If your email is registered, you will receive your User ID.'
                };
            }

            // Send email with the userId
            const emailSent = await this.mailService.sendUserId(user.email, user.userId);

            if (!emailSent) {
                throw new HttpException(
                    'Failed to send email. Please try again.',
                    HttpStatus.INTERNAL_SERVER_ERROR
                );
            }

            return {
                success: true,
                message: 'Your User ID has been sent to your email.'
            };
        } catch (error) {
            throw new HttpException(
                'Failed to process request',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Send notifications to all admin users about a tutor verification
     * @param tutor The tutor user who verified their account
     */
    private async notifyAdminsAboutTutorVerification(tutor: User): Promise<void> {
        try {
            // Get all admin users
            const adminUsers = await this.userService.getAllAdminUsers();

            if (!adminUsers || adminUsers.length === 0) {
                this.logger.warn('No admin users found to notify about tutor verification');
                return;
            }

            // Create notification title and message
            const title = 'New Tutor Verification';
            const message = `Tutor ${tutor.name} has verified their account and is waiting for your approval.`;

            // Use the DeeplinkService to generate the profile button HTML
            const profileButton = this.deeplinkService.getLinkHtml(
                DeeplinkType.PROFILE,
                {
                    userId: tutor.id,
                    userType: UserType.TUTOR,
                    linkText: 'Review Tutor Profile',
                    buttonStyle: true
                }
            );

            // Create HTML content for email with profile link
            const htmlContent = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #333;">New Tutor Verification</h2>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <p>Hello Admin,</p>
                        <p>Tutor <strong>${tutor.name}</strong> has verified their account and is waiting for your approval.</p>
                        <p>Please review their profile by clicking the button below:</p>
                        <div style="text-align: center; margin: 30px 0;">
                            ${profileButton}
                        </div>
                        <p>Tutor Details:</p>
                        <ul>
                            <li>Name: ${tutor.name}</li>
                            <li>User ID: ${tutor.userId}</li>
                            <li>Email: ${tutor.email}</li>
                            <li>Phone: ${tutor.phoneNumber}</li>
                        </ul>
                    </div>
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                        <p>This is an automated message from the HEC system.</p>
                        <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
                    </div>
                </div>
            `;

            // Get admin user IDs
            const adminUserIds = adminUsers.map(admin => admin.id);

            // Send a single notification to all admins (common notification)
            await this.notificationHelper.notifyMany(
                adminUserIds,
                NotificationType.TUTOR_VERIFICATION,
                title,
                message,
                {
                    relatedEntityId: tutor.id,
                    relatedEntityType: 'tutor',
                    htmlContent: htmlContent,
                    sendEmail: true,
                    sendInApp: true,
                    sendPush: false,
                    sendMobile: false,
                    sendSms: false,
                    sendRealtime: false
                }
            );

            this.logger.log(`Sent tutor verification notifications to ${adminUserIds.length} admin users for tutor ${tutor.name} (${tutor.id})`);
        } catch (error) {
            this.logger.error(`Failed to send tutor verification notifications: ${error.message}`, error.stack);
            // Don't rethrow the error to prevent disrupting the main flow
        }
    }

    async resendVerificationEmail(resendVerificationEmailDto: ResendVerificationEmailDto) {
        try {
            // Find user by email
            const user = await this.userRepository.findOne({ where: { email: resendVerificationEmailDto.email } });
            if (!user) {
                // For security reasons, don't reveal that the user doesn't exist
                return {
                    success: true,
                    message: 'If your email is registered, a new verification link has been sent.'
                };
            }

            // Check if user is already confirmed
            if (user.isConfirmed) {
                return {
                    success: false,
                    message: 'Your email is already verified. You can now login to your account.'
                };
            }

            // Delete any existing verification tokens for this user
            await this.emailVerificationRepository.delete({ userId: user.id });

            // Generate a secure random token
            const token = crypto.randomBytes(32).toString('hex');

            // Save email verification token
            const emailVerification = new EmailVerification();
            emailVerification.userId = user.id;
            emailVerification.token = token;
            emailVerification.expirationTime = addMinutesUTC(getCurrentUTCDate(), 5); // 5 minutes expiration
            emailVerification.isUsed = false;


            await this.emailVerificationRepository.save(emailVerification);

            // Send verification link to the user's email
            const emailSent = await this.mailService.sendVerificationLink(user.email, token);

            if (!emailSent) {
                throw new HttpException(
                    'Failed to send verification email. Please try again.',
                    HttpStatus.INTERNAL_SERVER_ERROR
                );
            }

            return {
                success: true,
                message: 'A new verification link has been sent to your email.'
            };
        } catch (error) {
            throw new HttpException(
                error.message || 'Failed to resend verification email',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Change user password
     * @param changePasswordDto Change password data
     * @param request Request object for IP and user agent information
     * @returns Login response with new token
     */
    async changePassword(changePasswordDto: ChangePasswordDto, request?: Request) {
        try {
            // Validate that new passwords match
            if (changePasswordDto.newPassword !== changePasswordDto.confirmNewPassword) {
                throw new BadRequestException('New passwords do not match');
            }

            // Find the user
            const user = await this.userRepository.findOne({
                where: { id: changePasswordDto.userId },
                relations: ['userRoles', 'userRoles.role']
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            // Verify current password
            const isCurrentPasswordValid = user.verifyPassword(changePasswordDto.currentPassword);
            if (!isCurrentPasswordValid) {
                throw new BadRequestException('Current password is incorrect');
            }

            // Check if new password is different from current password
            const isSamePassword = user.verifyPassword(changePasswordDto.newPassword);
            if (isSamePassword) {
                throw new BadRequestException('New password must be different from current password');
            }

            // Update password
            user.setPassword(changePasswordDto.newPassword);

            // Handle remember me functionality for new session
            if (changePasswordDto.rememberMe) {
                // Generate refresh token
                const refreshToken = crypto.randomBytes(40).toString('hex');

                // Set refresh token expiry (30 days)
                const refreshTokenExpiry = new Date();
                refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30);

                // Save refresh token to user
                user.refreshToken = refreshToken;
                user.refreshTokenExpiry = refreshTokenExpiry;
            } else {
                // Clear any existing refresh token
                user.refreshToken = '';
                user.refreshTokenExpiry = new Date(0);
            }

            // Update last login time since we're issuing a new token
            user.lastLoginAt = new Date();

            // Save user with new password and token settings
            await this.userRepository.save(user);

            // Send password change notification email
            try {
                const changeTime = new Date();
                const ipAddress = request?.ip || request?.connection?.remoteAddress || 'Unknown';
                const userAgent = request?.get('User-Agent') || 'Unknown';

                await this.mailService.sendPasswordChangeNotification(
                    user.email,
                    user.name,
                    changeTime,
                    ipAddress,
                    userAgent
                );

                this.logger.log(`Password change notification email sent to ${user.email}`);
            } catch (emailError) {
                // Log email error but don't fail the password change
                this.logger.error(`Failed to send password change notification email to ${user.email}: ${emailError.message}`);
            }

            // Get user roles
            const userRoles = user.userRoles ? user.userRoles.map(userRole => userRole.role.name) : [];

            // Determine user's primary role
            let selectedRole = user.type;
            if (user.type === UserType.ADMIN || userRoles.includes('admin')) {
                selectedRole = UserType.ADMIN;
            } else if (user.type === UserType.TUTOR || userRoles.includes('tutor')) {
                selectedRole = UserType.TUTOR;
            } else {
                selectedRole = UserType.STUDENT;
            }

            // Get active subscription plan for student users
            let activePlan: string | undefined = undefined;
            let planInfo: Record<string, any> = {};

            if (selectedRole === UserType.STUDENT) {
                try {
                    const userPlan = await this.userPlanRepository.findOne({
                        where: { userId: user.id, isActive: true },
                        relations: ['plan']
                    });

                    if (userPlan && userPlan.plan) {
                        activePlan = userPlan.plan.name;
                        const now = new Date();
                        const isActive = userPlan.endDate > now;

                        planInfo = {
                            planId: userPlan.planId,
                            planType: userPlan.plan.type,
                            planExpiryDate: userPlan.endDate.toISOString(),
                            planActive: isActive
                        };
                    }
                } catch (err) {
                    this.logger.error(`Error fetching subscription plan for user ${user.id}: ${err.message}`);
                }
            }

            // Get default skin ID for student users
            let defaultSkinId = null;
            if (selectedRole === UserType.STUDENT) {
                try {
                    const diaryResult = await this.dataSource.query(
                        `SELECT default_skin_id FROM diary WHERE user_id = $1 LIMIT 1`,
                        [user.id]
                    );

                    if (diaryResult && diaryResult.length > 0) {
                        defaultSkinId = diaryResult[0].default_skin_id;
                    }
                } catch (err) {
                    this.logger.error(`Error fetching diary for user ${user.id}: ${err.message}`);
                }
            }

            // Generate JWT token
            const payload: JwtPayload = {
                id: user.id,
                username: user.userId,
                sub: user.id,
                name: user.name,
                type: user.type,
                selectedRole: selectedRole,
                roles: userRoles,
                defaultSkinId: defaultSkinId,
                activePlan: activePlan,
                ...planInfo
            };

            // Set token expiration based on remember me
            const tokenExpiresIn = changePasswordDto.rememberMe ? '30d' : '1d';

            // Generate token and decode to get actual expiry time
            const token = this.jwtService.sign(payload, { expiresIn: tokenExpiresIn });
            const decoded = this.jwtService.decode(token) as { exp: number };
            const tokenExpiryDate = new Date(decoded.exp * 1000);

            // Get user DTO
            const userDto = user.toDto(selectedRole);
            userDto.defaultSkinId = defaultSkinId;

            // Add profile picture URL if the user has a profile picture
            const hasProfilePicture = await this.profilePictureService.hasProfilePicture(user.id);
            if (hasProfilePicture) {
                userDto.profilePictureUrl = await this.profilePictureService.getProfilePictureDirectUrl(user.id);
            } else {
                userDto.profilePictureUrl = null;
            }

            const response = {
                access_token: token,
                token_expires: tokenExpiryDate.toISOString(),
                user: userDto,
                returnUrl: '/home'
            };

            // Add refresh token to response if remember me is enabled
            if (changePasswordDto.rememberMe) {
                response['refresh_token'] = user.refreshToken;
                response['refresh_token_expires'] = user.refreshTokenExpiry;
            }

            this.logger.log(`Password changed successfully for user: ${user.userId}`);
            return response;

        } catch (error) {
            // For specific exceptions, pass them through
            if (error instanceof BadRequestException ||
                error instanceof NotFoundException ||
                error instanceof UnauthorizedException) {
                throw error;
            }

            // For all other errors, log and return generic message
            this.logger.error(`Error changing password for user ${changePasswordDto.userId}: ${error.message}`, error.stack);
            throw new HttpException(
                'An error occurred while changing password. Please try again.',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
