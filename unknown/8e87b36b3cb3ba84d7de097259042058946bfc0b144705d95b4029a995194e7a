import { MigrationInterface, QueryRunner } from "typeorm";

export class EssaySubmissionTask1748940258684 implements MigrationInterface {
    name = 'EssaySubmissionTask1748940258684'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task_id" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task_id" SET NOT NULL`);
    }

}
